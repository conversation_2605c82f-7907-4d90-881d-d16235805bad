-- ========================================
-- SQL性能测试脚本
-- ========================================

-- 1. 原始慢SQL（用于对比）
-- ========================================
SELECT
 t1.id,
 t1.object_id as "objectId",
 t1.alarm_info as "alarmInfo",
 t1.alarm_time AS "alarmTime",
 t1.alarm_state AS "alarmState",
 t1.start_time AS "startTime",
 t1.end_time AS "endTime",
 t1.exception_type AS "exceptionType",
 ( SELECT COUNT ( distinct(exception_id) ) FROM data_quality_alarm_exception t4 WHERE t1.id = t4.alarm_id ) AS "exceptionCount",
 (SELECT concat(round(SUM( t5.duration ) / 60,2),' min') 
 FROM
  data_quality_alarm_exception t4
  LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id 
 WHERE
  t4.alarm_id = t1.id 
 ) AS "duration",
 t2.site_name AS "siteName" 
FROM
 data_quality_alarm t1
 LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id 
 AND t1.alarm_object_type = 4
WHERE t1.alarm_object_type = 4 
  AND t2.id in ('367661a3b8d0452aa83481228f46870f','vbxoDjiNhDW1RriPqt6FvM','t1xIdEHAjOaY7D7Dsb2gGg','ff5cb3c8d6d641b1af0e3af6db9dfb45','JhNJgsbfhxynpypoQTY6Kg','9f34bd0d5d7d47eb954ae270074f63f7','8y0gEPTxgNqqBrVCNYEs4M','431b92812bb245c6ac526229cb23aae3') 
  AND t1.alarm_time >= '2025-07-28 00:00:00' 
  AND t1.alarm_time <= '2025-07-28 12:39:25.999' 
ORDER BY t1.alarm_time DESC 
LIMIT 20;

-- 2. 优化后的SQL（推荐使用）
-- ========================================
SELECT
 t1.id,
 t1.object_id as "objectId",
 t1.alarm_info as "alarmInfo",
 t1.alarm_time AS "alarmTime",
 t1.alarm_state AS "alarmState",
 t1.start_time AS "startTime",
 t1.end_time AS "endTime",
 t1.exception_type AS "exceptionType",
 COALESCE(t3.exceptionCount, 0) AS "exceptionCount",
 COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS "duration",
 t2.site_name AS "siteName" 
FROM
 data_quality_alarm t1
 LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id 
 AND t1.alarm_object_type = 4
 LEFT JOIN (
   SELECT 
     t4.alarm_id,
     COUNT(DISTINCT t4.exception_id) as exceptionCount,
     SUM(COALESCE(t5.duration, 0)) as totalDuration
   FROM data_quality_alarm_exception t4
   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id
   GROUP BY t4.alarm_id
 ) t3 ON t1.id = t3.alarm_id
WHERE t1.alarm_object_type = 4 
  AND t2.id in ('367661a3b8d0452aa83481228f46870f','vbxoDjiNhDW1RriPqt6FvM','t1xIdEHAjOaY7D7Dsb2gGg','ff5cb3c8d6d641b1af0e3af6db9dfb45','JhNJgsbfhxynpypoQTY6Kg','9f34bd0d5d7d47eb954ae270074f63f7','8y0gEPTxgNqqBrVCNYEs4M','431b92812bb245c6ac526229cb23aae3') 
  AND t1.alarm_time >= '2025-07-28 00:00:00' 
  AND t1.alarm_time <= '2025-07-28 12:39:25.999' 
ORDER BY t1.alarm_time DESC 
LIMIT 20;

-- 3. 进一步优化的SQL（如果有site_id冗余字段）
-- ========================================
SELECT
 t1.id,
 t1.object_id as "objectId",
 t1.alarm_info as "alarmInfo",
 t1.alarm_time AS "alarmTime",
 t1.alarm_state AS "alarmState",
 t1.start_time AS "startTime",
 t1.end_time AS "endTime",
 t1.exception_type AS "exceptionType",
 COALESCE(t3.exceptionCount, 0) AS "exceptionCount",
 COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS "duration",
 t2.site_name AS "siteName" 
FROM
 data_quality_alarm t1
 LEFT JOIN bcs_sites t2 ON t1.site_id = t2.id  -- 假设添加了site_id字段
 LEFT JOIN (
   SELECT 
     t4.alarm_id,
     COUNT(DISTINCT t4.exception_id) as exceptionCount,
     SUM(COALESCE(t5.duration, 0)) as totalDuration
   FROM data_quality_alarm_exception t4
   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id
   GROUP BY t4.alarm_id
 ) t3 ON t1.id = t3.alarm_id
WHERE t1.alarm_object_type = 4 
  AND t1.site_id in ('367661a3b8d0452aa83481228f46870f','vbxoDjiNhDW1RriPqt6FvM','t1xIdEHAjOaY7D7Dsb2gGg','ff5cb3c8d6d641b1af0e3af6db9dfb45','JhNJgsbfhxynpypoQTY6Kg','9f34bd0d5d7d47eb954ae270074f63f7','8y0gEPTxgNqqBrVCNYEs4M','431b92812bb245c6ac526229cb23aae3') 
  AND t1.alarm_time >= '2025-07-28 00:00:00' 
  AND t1.alarm_time <= '2025-07-28 12:39:25.999' 
ORDER BY t1.alarm_time DESC 
LIMIT 20;

-- 4. 测试不同时间范围的SQL
-- ========================================

-- 4.1 查询今天的数据
SELECT
 t1.id,
 t1.object_id as "objectId",
 t1.alarm_info as "alarmInfo",
 t1.alarm_time AS "alarmTime",
 t1.alarm_state AS "alarmState",
 t1.start_time AS "startTime",
 t1.end_time AS "endTime",
 t1.exception_type AS "exceptionType",
 COALESCE(t3.exceptionCount, 0) AS "exceptionCount",
 COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS "duration",
 t2.site_name AS "siteName" 
FROM
 data_quality_alarm t1
 LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id 
 AND t1.alarm_object_type = 4
 LEFT JOIN (
   SELECT 
     t4.alarm_id,
     COUNT(DISTINCT t4.exception_id) as exceptionCount,
     SUM(COALESCE(t5.duration, 0)) as totalDuration
   FROM data_quality_alarm_exception t4
   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id
   GROUP BY t4.alarm_id
 ) t3 ON t1.id = t3.alarm_id
WHERE t1.alarm_object_type = 4 
  AND t2.id in ('367661a3b8d0452aa83481228f46870f','vbxoDjiNhDW1RriPqt6FvM','t1xIdEHAjOaY7D7Dsb2gGg','ff5cb3c8d6d641b1af0e3af6db9dfb45','JhNJgsbfhxynpypoQTY6Kg','9f34bd0d5d7d47eb954ae270074f63f7','8y0gEPTxgNqqBrVCNYEs4M','431b92812bb245c6ac526229cb23aae3') 
  AND t1.alarm_time >= CURRENT_DATE
  AND t1.alarm_time < CURRENT_DATE + INTERVAL '1 day'
ORDER BY t1.alarm_time DESC 
LIMIT 50;

-- 4.2 查询最近7天的数据
SELECT
 t1.id,
 t1.object_id as "objectId",
 t1.alarm_info as "alarmInfo",
 t1.alarm_time AS "alarmTime",
 t1.alarm_state AS "alarmState",
 t1.start_time AS "startTime",
 t1.end_time AS "endTime",
 t1.exception_type AS "exceptionType",
 COALESCE(t3.exceptionCount, 0) AS "exceptionCount",
 COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS "duration",
 t2.site_name AS "siteName" 
FROM
 data_quality_alarm t1
 LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id 
 AND t1.alarm_object_type = 4
 LEFT JOIN (
   SELECT 
     t4.alarm_id,
     COUNT(DISTINCT t4.exception_id) as exceptionCount,
     SUM(COALESCE(t5.duration, 0)) as totalDuration
   FROM data_quality_alarm_exception t4
   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id
   GROUP BY t4.alarm_id
 ) t3 ON t1.id = t3.alarm_id
WHERE t1.alarm_object_type = 4 
  AND t2.id in ('367661a3b8d0452aa83481228f46870f','vbxoDjiNhDW1RriPqt6FvM','t1xIdEHAjOaY7D7Dsb2gGg','ff5cb3c8d6d641b1af0e3af6db9dfb45','JhNJgsbfhxynpypoQTY6Kg','9f34bd0d5d7d47eb954ae270074f63f7','8y0gEPTxgNqqBrVCNYEs4M','431b92812bb245c6ac526229cb23aae3') 
  AND t1.alarm_time >= CURRENT_DATE - INTERVAL '7 days'
  AND t1.alarm_time < CURRENT_DATE + INTERVAL '1 day'
ORDER BY t1.alarm_time DESC 
LIMIT 100;

-- 5. 性能测试命令
-- ========================================

-- 5.1 查看执行计划（PostgreSQL）
EXPLAIN ANALYZE 
-- 在这里粘贴上面的SQL

-- 5.2 查看执行计划（MySQL）
EXPLAIN FORMAT=JSON
-- 在这里粘贴上面的SQL

-- 5.3 查看执行计划（Oracle）
EXPLAIN PLAN FOR
-- 在这里粘贴上面的SQL
-- SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 6. 索引创建脚本（建议先测试再创建）
-- ========================================

-- 6.1 基础索引
CREATE INDEX IF NOT EXISTS idx_alarm_object_type_time ON data_quality_alarm(alarm_object_type, alarm_time DESC);
CREATE INDEX IF NOT EXISTS idx_alarm_exception_alarm_id ON data_quality_alarm_exception(alarm_id);
CREATE INDEX IF NOT EXISTS idx_alarm_exception_exception_id ON data_quality_alarm_exception(exception_id);
CREATE INDEX IF NOT EXISTS idx_exception_id ON data_quality_exception(id);
CREATE INDEX IF NOT EXISTS idx_sites_id ON bcs_sites(id);

-- 6.2 复合索引（可选，根据测试结果决定是否创建）
CREATE INDEX IF NOT EXISTS idx_alarm_cover ON data_quality_alarm(alarm_object_type, alarm_time DESC, id, object_id);
CREATE INDEX IF NOT EXISTS idx_alarm_exception_cover ON data_quality_alarm_exception(alarm_id, exception_id);

-- 7. 统计信息更新（建议在测试前执行）
-- ========================================

-- PostgreSQL
ANALYZE data_quality_alarm;
ANALYZE data_quality_alarm_exception;
ANALYZE data_quality_exception;
ANALYZE bcs_sites;

-- MySQL
ANALYZE TABLE data_quality_alarm;
ANALYZE TABLE data_quality_alarm_exception;
ANALYZE TABLE data_quality_exception;
ANALYZE TABLE bcs_sites;

-- Oracle
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'DATA_QUALITY_ALARM');
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'DATA_QUALITY_ALARM_EXCEPTION');
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'DATA_QUALITY_EXCEPTION');
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'BCS_SITES');
