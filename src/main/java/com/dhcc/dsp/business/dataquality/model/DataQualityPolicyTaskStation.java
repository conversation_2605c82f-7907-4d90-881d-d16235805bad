package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("dq_policy_task_station")
public class DataQualityPolicyTaskStation implements Serializable {

    private static final long serialVersionUID = -9121020650993524809L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("task_id")
    private String taskId;

    @TableField("site_id")
    private String siteId;

    @TableField("station_number")
    private String stationNumber;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    @TableField("invalid_values")
    private String invalidValues;

    @TableField("type")
    private String type;
}
