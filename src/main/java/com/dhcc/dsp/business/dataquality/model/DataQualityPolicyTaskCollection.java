package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("dq_policy_task_collection")
public class DataQualityPolicyTaskCollection implements Serializable {

    private static final long serialVersionUID = 8140088609114907141L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("task_id")
    private String taskId;

    @TableField("site_id")
    private String siteId;

    @TableField("collection_name")
    private String collectionName;

}
