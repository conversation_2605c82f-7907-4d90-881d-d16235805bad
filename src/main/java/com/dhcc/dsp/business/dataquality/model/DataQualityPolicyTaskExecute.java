package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName("dq_policy_task_execute")
@Data
public class DataQualityPolicyTaskExecute implements Serializable {

    private static final long serialVersionUID = -3328345336853264711L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("task_id")
    private String taskId;

    @TableField("execute_time")
    private Date executeTime;

    @TableField("execute_status")
    private Integer executeStatus;

    @TableField("execute_type")
    private Integer executeType;

    @TableField("execute_result")
    private String executeResult = "执行成功";

    @TableField("is_merged")
    private boolean merged = false;

    @TableField("start_time")
    private String startTime;

    @TableField("end_time")
    private String endTime;


    /**
     * 类型，0-时序稽核，1-结构化稽核
     */
    @TableField("type")
    private Integer type;

}
