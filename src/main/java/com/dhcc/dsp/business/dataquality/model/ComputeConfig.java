package com.dhcc.dsp.business.dataquality.model;

import com.dhcc.avatar.domain.gics.StepEntity;
import com.dhcc.avatar.resource.ResourceType;
import com.dhcc.dsp.business.model.DataSourceEntity;
import lombok.Data;

@Data
public class ComputeConfig {

    /**
     * 数据源类型
     */
    private ResourceType resourceType;

    /**
     * sql
     */
    private String sqlStatement;

    /**
     * 数据库连接
     */
    private DataSourceEntity dataSourceEntity;

    /**
     * 输入算子
     */
    private StepEntity stepEntity;
}
