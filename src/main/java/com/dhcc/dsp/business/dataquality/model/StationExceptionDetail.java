package com.dhcc.dsp.business.dataquality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class StationExceptionDetail {

    @ExcelProperty(value = "异常类型", index = 1)
    private String exceptionType;

    @ExcelProperty(value = "开始时间", index = 2)
    private String startTime;

    @ExcelProperty(value = "结束时间", index = 3)
    private String endTime;

    @ExcelProperty(value = "持续时长", index = 4)
    private String duration;

    @ExcelProperty(value = "设备名称", index = 5)
    private String deviceName;

    @ExcelProperty(value = "厂站名称", index = 6)
    private String siteName;

    @ExcelProperty(value = "原始测点名称", index = 7)
    private String stationName;

    @ExcelProperty(value = "原始测点编码", index = 8)
    private String stationNumber;

    @ExcelIgnore
    private String siteId;

}
