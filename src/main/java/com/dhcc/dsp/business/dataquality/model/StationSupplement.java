package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("dq_station_supplement")
public class StationSupplement implements Serializable {
    private static final long serialVersionUID = 211916651843912015L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("supplement_task_id")
    private String supplementTaskId;

    @TableField("station_number")
    private String stationNumber;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;
}
