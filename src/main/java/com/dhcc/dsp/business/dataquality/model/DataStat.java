package com.dhcc.dsp.business.dataquality.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据统计
 */
@Data
public class DataStat {

    @ExcelProperty(value = "kkscode", index = 0)
    private String kkscode;
    @ExcelProperty(value = "type", index = 1)
    private String type;
    @ExcelProperty(value = "min", index = 2)
    private Double min;
    @ExcelProperty(value = "max", index = 3)
    private Double max;
    @ExcelProperty(value = "avg", index = 4)
    private Double avg;
    @ExcelProperty(value = "k0", index = 5)
    private Integer k0;
    @ExcelProperty(value = "k1", index = 6)
    private Integer k1;
    @ExcelProperty(value = "k2", index = 7)
    private Integer k2;
    @ExcelProperty(value = "k3", index = 8)
    private Integer k3;
    @ExcelProperty(value = "k4", index = 9)
    private Integer k4;
    @ExcelProperty(value = "k5", index = 10)
    private Integer k5;
    @ExcelProperty(value = "k6", index = 11)
    private Integer k6;
    @ExcelProperty(value = "k7", index = 12)
    private Integer k7;
    @ExcelProperty(value = "k8", index = 13)
    private Integer k8;
    @ExcelProperty(value = "k9", index = 14)
    private Integer k9;
    @ExcelProperty(value = "k10", index = 15)
    private Integer k10;
    @ExcelProperty(value = "k11", index = 16)
    private Integer k11;
    @ExcelProperty(value = "k12", index = 17)
    private Integer k12;
    @ExcelProperty(value = "k13", index = 18)
    private Integer k13;
    @ExcelProperty(value = "k14", index = 19)
    private Integer k14;
    @ExcelProperty(value = "k15", index = 20)
    private Integer k15;
    @ExcelProperty(value = "k16", index = 21)
    private Integer k16;
    @ExcelProperty(value = "k17", index = 22)
    private Integer k17;
    @ExcelProperty(value = "k18", index = 23)
    private Integer k18;
    @ExcelProperty(value = "k19", index = 24)
    private Integer k19;
    @ExcelProperty(value = "k20", index = 25)
    private Integer k20;
    @ExcelProperty(value = "k21", index = 26)
    private Integer k21;
    @ExcelProperty(value = "k22", index = 27)
    private Integer k22;
    @ExcelProperty(value = "k23", index = 28)
    private Integer k23;

    public DataStat(String stationNumber, String type) {
        this.kkscode = stationNumber;
        this.type = type;
    }

    /**
     * 通过tsdb/data接口，resultType=RT_COUNT,queryExpression参数查询测点状态量为0的数量
     *
     * @param values
     */
    public void setKK0(List<JSONObject> values) {
        if (!StrUtil.equals("K", this.type)) {
            return;
        }
        if (CollUtil.isNotEmpty(values)) {
            JSONObject obj = values.get(0);
            this.k0 = obj.getInt("Value");
        } else {
            this.k0 = 0;
        }
    }

    /**
     * 通过tsdb/data接口，resultType=RT_COUNT,queryExpression参数查询测点状态量为1的数量
     *
     * @param values
     */
    public void setKK1(List<JSONObject> values) {
        if (!StrUtil.equals("K", this.type)) {
            return;
        }
        if (CollUtil.isNotEmpty(values)) {
            JSONObject obj = values.get(0);
            this.k1 = obj.getInt("Value");
        } else {
            this.k1 = 0;
        }
    }

    /**
     * 通过tsdb/data接口，resultType=RT_ALL参数查询测点的最大、最小、平均值以后处理数据
     *
     * @param values
     */
    public void setAStat(List<JSONObject> values) {
        if (!StrUtil.equals("A", this.type)) {
            return;
        }
        if (CollUtil.isNotEmpty(values)) {
            //key:Q,value:Value
            Map<Integer, Double> map = values.stream().collect(Collectors.toMap(v -> v.getInt("Q"), v -> v.getDouble("Value")));
            if (MapUtil.isNotEmpty(map)) {
                //最小值
                this.min = map.get(1);
                //平均值
                this.avg = map.get(2);
                //最大值
                this.max = map.get(3);
            }
        }
    }

    /**
     * 通过tsdb/data接口，sampled=3600&resultType=RT_COUNT参数查询测点的每小时数量
     */
    public void setACount(List<JSONObject> values) {
        if (!StrUtil.equals("A", this.type)) {
            return;
        }
        if (CollUtil.isNotEmpty(values)) {
            //key:时间戳的小时值，value:Value
            Map<Integer, Integer> map = values.stream().collect(Collectors.toMap(v -> DateUtil.hour(new Date(v.getLong("Time")), true), v -> v.getInt("Value")));
            this.k0 = map.getOrDefault(0, 0);
            this.k1 = map.getOrDefault(1, 0);
            this.k2 = map.getOrDefault(2, 0);
            this.k3 = map.getOrDefault(3, 0);
            this.k4 = map.getOrDefault(4, 0);
            this.k5 = map.getOrDefault(5, 0);
            this.k6 = map.getOrDefault(6, 0);
            this.k7 = map.getOrDefault(7, 0);
            this.k8 = map.getOrDefault(8, 0);
            this.k9 = map.getOrDefault(9, 0);
            this.k10 = map.getOrDefault(10, 0);
            this.k11 = map.getOrDefault(11, 0);
            this.k12 = map.getOrDefault(12, 0);
            this.k13 = map.getOrDefault(13, 0);
            this.k14 = map.getOrDefault(14, 0);
            this.k15 = map.getOrDefault(15, 0);
            this.k16 = map.getOrDefault(16, 0);
            this.k17 = map.getOrDefault(17, 0);
            this.k18 = map.getOrDefault(18, 0);
            this.k19 = map.getOrDefault(19, 0);
            this.k20 = map.getOrDefault(20, 0);
            this.k21 = map.getOrDefault(21, 0);
            this.k22 = map.getOrDefault(22, 0);
            this.k23 = map.getOrDefault(23, 0);
        } else {
            this.k0 = 0;
            this.k1 = 0;
            this.k2 = 0;
            this.k3 = 0;
            this.k4 = 0;
            this.k5 = 0;
            this.k6 = 0;
            this.k7 = 0;
            this.k8 = 0;
            this.k9 = 0;
            this.k10 = 0;
            this.k11 = 0;
            this.k12 = 0;
            this.k13 = 0;
            this.k14 = 0;
            this.k15 = 0;
            this.k16 = 0;
            this.k17 = 0;
            this.k18 = 0;
            this.k19 = 0;
            this.k20 = 0;
            this.k21 = 0;
            this.k22 = 0;
            this.k23 = 0;
        }
    }


}
