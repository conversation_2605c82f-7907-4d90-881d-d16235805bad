package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName("dq_policy_alarm")
@Data
public class DataQualityAlarm implements Serializable {

    private static final long serialVersionUID = -1332028732577990903L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("alarm_name")
    private String alarmName;

    @TableField("task_id")
    private String taskId;

    @TableField("execute_id")
    private String executeId;

    @TableField("rule_id")
    private String ruleId;

    @TableField("handle_status")
    private Integer handleStatus;

    @TableField("handle_user_id")
    private String handleUserId;

    @TableField("expected_completion_time")
    private Date expectedCompletionTime;

    @TableField("handle_time")
    private Date handleTime;

    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private Integer alarmLevel;

    @TableField(exist = false)
    private String siteName;

    @TableField(exist = false)
    private String alarmTime;

    @TableField(exist = false)
    private String conditionCn;


}
