package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("dq_station_supplement_task")
public class StationSupplementTask implements Serializable {

    private static final long serialVersionUID = -1053045894815669696L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("execute_time")
    private Date executeTime;

    @TableField("policy_task_id")
    private String policyTaskId;

    @TableField("execute_status")
    private Integer executeStatus = 1;

    @TableField("execute_message")
    private String executeMessage = "执行成功";
}
