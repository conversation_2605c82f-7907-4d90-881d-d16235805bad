package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("dq_policy_structured_result")
public class DataQualityStructuredResult implements Serializable {

    private static final long serialVersionUID = -7177369657822799466L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("quality_task_id")
    private String qualityTaskId;

    @TableField("execute_id")
    private String executeId;

    @TableField("data_collection_name")
    private String dataCollectionName;

    @TableField("column_name")
    private String columnName;

    @TableField("target_collection_name")
    private String targetCollectionName;

    @TableField("target_column_name")
    private String targetColumnName;

    @TableField("rule_type")
    private String ruleType;

    @TableField("business_category")
    private String businessCategory;

    @TableField("qualification_rate")
    private String qualificationRate;

    @TableField("unqualified_quantity")
    private Long unqualifiedQuantity;

    @TableField("total_quantity")
    private Long totalQuantity;

    @TableField("unqualified_detail")
    private String unqualifiedDetail;

    @TableField("execute_time")
    private Date executeTime;

    @TableField("row_unqualified_quantity")
    private Long rowUnqualifiedQuantity;

    @TableField("row_unqualified_quantity")
    private Long rowTotalQuantity;

    @TableField("row_unqualified_quantity")
    private Long columnUnqualifiedQuantity;

    @TableField("row_unqualified_quantity")
    private Long columnTotalQuantity;

    @TableField(exist = false)
    private String taskName;

    @TableField(exist = false)
    private String unqualifiedRate;


}
