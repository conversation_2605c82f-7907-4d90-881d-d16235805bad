package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("dq_policy_detail")
public class DataQualityPolicyDetail implements Serializable {

    private static final long serialVersionUID = 3123667773323243608L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("library_id")
    private String libraryId;

    @TableField("police_name")
    private String policeName;

    @TableField("audit_pro_name")
    private String auditProName;

    @TableField("business_define")
    private String businessDefine;

    @TableField("judge_define")
    private String judgeDefine;

    @TableField("other_desc")
    private String otherDesc;

    @TableField("sample")
    private String sample;

    @TableField("sample_pic")
    private String samplePic;

    @TableField("create_time")
    private String createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    private String updateTime;

    @TableField(exist = false)
    private String userName;

}
