package com.dhcc.dsp.business.dataquality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeviceTemplate implements Serializable {

    private static final long serialVersionUID = -9163195791325824763L;

    @ExcelIgnore
    private String id;

    @ExcelProperty(value = "厂站名称", index = 0)
    private String siteName;

    @ExcelProperty(value = "实例化设备", index = 1)
    private String name;

    @ExcelProperty(value = "设备KKS编码", index = 2)
    private String kksCode;

    @ExcelProperty(value = "逻辑设备路径", index = 3)
    private String logicPath;

}
