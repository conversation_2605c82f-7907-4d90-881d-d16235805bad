package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dhcc.dsp.common.model.BaseTreeNode;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("dq_policy_library")
public class DataQualityPolicyLibrary extends BaseTreeNode implements Serializable {

    private static final long serialVersionUID = 9037675265436677352L;

    @TableField("name")
    private String name;

    @TableField("type")
    private Integer type;

    @TableField("path")
    private String path;

}
