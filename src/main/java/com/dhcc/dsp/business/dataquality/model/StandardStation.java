package com.dhcc.dsp.business.dataquality.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@TableName("bcs_standard_station")
@Data
public class StandardStation implements Serializable {
    private static final long serialVersionUID = -5093235636731126264L;

    @ExcelIgnore
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ExcelProperty(value = "厂站ID", index = 17)
    @TableField(value = "site_id")
    private String siteId;

    @ExcelProperty(value = "厂站名称", index = 0)
    @TableField(exist = false)
    private String siteName;

    @ExcelProperty(value = "测点编码", index = 2)
    @TableField(value = "station_number")
    private String stationNumber;

    @ExcelProperty(value = "测点名称", index = 1)
    @TableField(value = "station_name")
    private String stationName;

    @ExcelProperty(value = "设备KKS编码", index = 9)
    @TableField(value = "kkscode")
    private String kksCode;

    @ExcelProperty(value = "设备名称", index = 7)
    @TableField(value = "device_name")
    private String deviceName;

    @ExcelProperty(value = "设备逻辑路径", index = 8)
    @TableField(value = "logic_path")
    private String devicePath;

    @ExcelProperty(value = "标准属性英文名", index = 5)
    @TableField(value = "en_name")
    private String bcsEnName;

    @ExcelProperty(value = "标准属性中文名", index = 4)
    @TableField(value = "cn_name")
    private String bcsCHName;

    @ExcelProperty(value = "属性分类", index = 6)
    @TableField(value = "attr_classify")
    private String abbrClassify;

    @ExcelProperty(value = "属性类型", index = 18)
    @TableField(value = "attr_type")
    private String abbrType;

    @ExcelProperty(value = "是否必填", index = 19)
    @TableField(value = "required")
    private String required;

    @ExcelProperty(value = "测点全局编码", index = 3)
    @TableField(value = "global_number")
    private String stationGlobalNumber;

    @ExcelProperty(value = "设备物理路径", index = 10)
    @TableField(value = "physics_device_path")
    private String physicsDevicePath;

    @ExcelProperty(value = "测点来源", index = 16)
    @TableField(value = "station_source")
    private String stationSource;

    @ExcelProperty(value = "上限值", index = 11)
    @TableField(value = "upper_value")
    private String upperValue;

    @ExcelProperty(value = "上上限值", index = 12)
    @TableField(value = "more_upper_value")
    private String moreUpperValue;

    @ExcelProperty(value = "下限值", index = 13)
    @TableField(value = "lower_value")
    private String lowerValue;

    @ExcelProperty(value = "下下限值", index = 14)
    @TableField(value = "more_lower_value")
    private String moreLowerValue;

    @ExcelProperty(value = "单位", index = 15)
    @TableField(value = "unit")
    private String unit;

    @ExcelProperty(value = "最新值时间", index = 26)
    @TableField(exist = false)
    private String latestTime;

    @ExcelProperty(value = "最新值", index = 25)
    @TableField(exist = false)
    private String latestValue;

    @ExcelIgnore
    @TableField(value = "create_data")
    private String createData;

    @ExcelProperty(value = "优先级", index = 20)
    @TableField(value = "seat_code")
    private String priority;

    @ExcelProperty(value = "LCU代码", index = 21)
    @TableField(value = "lcu_code")
    private String lcuCode;

    @ExcelProperty(value = "备注", index = 22)
    @TableField(value = "station_id")
    private String remark;

    @ExcelProperty(value = "额定值", index = 23)
    @TableField(exist = false)
    private String ratedValue;

    @ExcelProperty(value = "原逻辑名", index = 24)
    @TableField(exist = false)
    private String originalLogicalName;

    @ExcelProperty(value = "特征值", index = 27)
    @TableField(exist = false)
    private String featuresValue;

    @ExcelIgnore
    @TableField(exist = false)
    private List<StandardStation> children;

    @ExcelIgnore
    @TableField(exist = false)
    private String dataId;

    @ExcelProperty(value = "开始时间", index = 28)
    @TableField(exist = false)
    private String startTime;

    @ExcelProperty(value = "结束时间", index = 29)
    @TableField(exist = false)
    private String endTime;

    @ExcelProperty(value = "任务名称", index = 30)
    @TableField(exist = false)
    private String taskName;

    @ExcelProperty(value = "策略名称", index = 31)
    @TableField(exist = false)
    private String policyName;

}
