package com.dhcc.dsp.business.dataquality.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.dhcc.avatar.common.SystemConstants.qError;

@Data
@TableName("dq_policy_task")
public class DataQualityPolicyTask implements Serializable {

    private static final long serialVersionUID = -7591026768355654595L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("task_name")
    private String taskName;

    @TableField("task_desc")
    private String taskDesc;

    @TableField("business_type")
    private String businessType;

    @TableField("create_user")
    private String createUser;

    @TableField("create_time")
    private String createTime;

    @TableField("update_time")
    private String updateTime;

    @TableField("status")
    private Integer status;

    @TableField("execute_times")
    private Long executeTimes;

    @TableField("exceptions_times")
    private Long exceptionsTimes;

    @TableField("execute_type")
    private Integer executeType;

    @TableField("start_time")
    private Date startTime;

    @TableField("time_range")
    private String timeRange;

    @TableField("retrieval_cycle")
    private Long retrievalCycle;

    @TableField("execute_cron")
    private String executeCron;

    @TableField("history_time_range")
    private String historyTimeRange;

    @TableField("history_execute_status")
    private Integer historyExecuteStatus;

    @TableField("sub_task_station_count")
    private Integer subTaskStationCount;

    @TableField("exceptions_condition")
    private String exceptionsCondition;

    @TableField("path")
    private String path;

    @TableField("last_execute_time")
    private Date lastExecuteTime;

    @TableField("library_id")
    private String libraryId;

    @TableField("execute_status")
    private String executeStatus;

    @TableField("application_id")
    private String applicationId;

    @TableField("execute_model_id")
    private String executeModelId;

    /**
     * 任务类型，0-时序稽核，1-结构化稽核
     */
    @TableField("task_type")
    private Integer taskType;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private Double errorValue = qError;
}
