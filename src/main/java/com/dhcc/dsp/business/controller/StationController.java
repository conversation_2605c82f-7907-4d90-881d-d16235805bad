package com.dhcc.dsp.business.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.dsp.business.service.StationService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.Map;
import java.util.Objects;

@Api(value = "测点相关服务调用", tags = "station")
@RestController
@RequestMapping("${api.version}/station")
public class StationController {

    private static final Log log = LogFactory.get();

    private final StationService stationService;

    public StationController(StationService stationService) {
        this.stationService = stationService;
    }

    @PostMapping("/getStationByCondition")
    public Map<String, Object> getStationByCondition(HttpServletRequest request, String condition, Integer page, Integer limit) {
        log.info("开始根据条件获取测点数据.............");
        if (StrUtil.isNotBlank(condition)) {
            condition = URLUtil.decode(Base64.decodeStr(condition));
        }

        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = stationService.getStationByCondition(token, condition, page, limit);

        log.info("测点数据获取完毕");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("totalCount"))).put("page", page)).put("data", rsMap.get("rsList"));
    }

    @ApiOperation(value = "获取设备测点信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteId", value = "厂站ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "deviceId", value = "设备ID", required = true, dataType = "String"),
    })
    @PostMapping("/getStation")
    public Map<String, Object> getStationData(HttpServletRequest request, String siteId, String deviceNameStr, boolean isGlobal, Integer page, Integer limit, String searchCondition, String requiredModelCondition, String optionalModelCondition) {
        log.info("开始获取测点数据.............");
        searchCondition = URLUtil.decode(searchCondition);
        requiredModelCondition = URLUtil.decode(requiredModelCondition);
        optionalModelCondition = URLUtil.decode(optionalModelCondition);

        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = stationService.getStationData(token, siteId, deviceNameStr, isGlobal, page, limit, searchCondition, requiredModelCondition, optionalModelCondition);

        log.info("测点数据获取完毕");
        if (rsMap.size() == 0) {
            return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("total", 0)).put("page", page)).put("data", "");
        }

        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("totalCount"))).put("page", page)).put("data", rsMap.get("rsList"));
    }

    @ApiOperation(value = "获取设备路径树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteList", value = "厂站列表", required = true, dataType = "String"),
            @ApiImplicitParam(name = "access_token", value = "令牌", required = true, dataType = "String")
    })
    @PostMapping("/getAbbrClassifyNum")
    public Map<String, Object> getAbbrClassifyNumData(String siteList, String devicePath, String abbrSearchCondition) {
        log.info("开始调用系统服务获取设备路径数据.............");

        siteList = URLUtil.decode(siteList);
        abbrSearchCondition = URLUtil.decode(abbrSearchCondition);

        Map<String, Object> rsMap = stationService.getAbbrClassifyNumData(siteList, devicePath, abbrSearchCondition);

        log.info("设备路径数据获取完毕");
        return JsonResult.ok().put("data", rsMap);
    }

    @ApiOperation(value = "获取设备路径对应属性统计值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "devicePath", value = "厂站列表", required = true, dataType = "String"),
            @ApiImplicitParam(name = "access_token", value = "令牌", required = true, dataType = "String")
    })
    @PostMapping("/getAbbrClassifyDetail")
    public Map<String, Object> getAbbrClassifyDetailData(String siteList, String devicePath, String abbrClassify, String abbrSearchCondition, Integer page, Integer limit) {
        log.info("开始调用系统服务获取设备属性数据.............");

        siteList = URLUtil.decode(siteList);
        abbrSearchCondition = URLUtil.decode(abbrSearchCondition);

        Map<String, Object> rsMap = stationService.getAbbrClassifyDetailData(siteList, devicePath, abbrClassify, abbrSearchCondition, page, limit);

        log.info("设备属性数据获取完毕");
        return Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("rsList"))).put("totalCount", rsMap.get("totalCount"));
    }

    @ApiOperation(value = "获取测点时序数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stationSelectList", value = "所选择的测点列表，json格式", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stime", value = "开始时间，YYYY-MM-DD HH:MM:SS格式", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etime", value = "结束时间，YYYY-MM-DD HH:MM:SS格式", required = true, dataType = "String"),
            @ApiImplicitParam(name = "datasource", value = "数据源，0为hbase，1为influxdb", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sampled", value = "采样步长（单位：秒），按以下两种情况设定值： sampled>0 - 按该采样步长间隔稀疏值返回 sampled=0 - 不进行采样 sampled", dataType = "String"),
            @ApiImplicitParam(name = "fillPoint", value = "补值策略：1: 线性补值, 0: 前值补值, -1：不补值", dataType = "String")
    })
    @PostMapping("/getTimeSeries")
    public Map<String, Object> getTimeSeriesData(HttpServletRequest request, String dataSetName, String stationSelectList, String stime, String etime, Integer pageNumber, Integer pageSize, String sampled, String fillPoint, String datasource) {
        log.info("开始查询测点时序数据.............");

        stationSelectList = URLUtil.decode(stationSelectList);

        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = stationService.getTimeSeriesData(token, dataSetName, stationSelectList, stime, etime, pageNumber, pageSize, datasource, "", sampled, "", fillPoint, "", "","","");

        log.info("测点时序数据获取完毕");

        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }

    @ApiOperation(value = "获取时序数据明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "conditon", value = "传入测点查询相应条件,以json的格式传入，json格式为：{\n" +
                    "    \"dataSetName\": \"\",\n" +
                    "    \"stationSelectList\": \"\",\n" +
                    "    \"stime\": \"\",\n" +
                    "    \"etime\": \"\",\n" +
                    "    \"datasource\": \"\",\n" +
                    "    \"pageNumber\": 1,\n" +
                    "    \"pageSize\": 10\n" +
                    "}", required = true, dataType = "string", paramType = "body")
    })
    @PostMapping("/getTimeSeriesByCondition")
    public Map<String, Object> getTimeSeriesByCondition(HttpServletRequest request , @NotBlank(message = "条件不允许为空") @RequestBody String conditon) {
        log.info("开始查询测点时序数据.............");
        JSONObject jsonObject = JSONUtil.parseObj(conditon);
        String dataSetName = jsonObject.getStr("siteId");
        String stationSelectList = jsonObject.getStr("stationSelectList");
        String stime = jsonObject.getStr("stime");
        String etime = jsonObject.getStr("etime");
        String sampled = jsonObject.getStr("sampled");
        String fillPoint = jsonObject.getStr("fillPoint");
        String datasource = jsonObject.getStr("datasource");
        Integer pageNumber = jsonObject.getInt("pageNumber");
        Integer pageSize = jsonObject.getInt("pageSize");
        String filterQ = jsonObject.getStr("filterQ");
        String resultType = jsonObject.getStr("resultType");
        String isStats = jsonObject.getStr("isStats");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = stationService.getTimeSeriesData(token, dataSetName, stationSelectList, stime, etime, pageNumber, pageSize, datasource, "", sampled, resultType, fillPoint, filterQ, isStats,"","");

        log.info("测点时序数据获取完毕");

        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }

}
