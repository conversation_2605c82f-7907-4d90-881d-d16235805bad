package com.dhcc.dsp.business.controller;

import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.controller.BaseController;
import com.dhcc.dsp.business.service.DataReuseService;
import com.dhcc.dsp.common.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Map;
import java.util.Objects;

import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

/**
 * 数据服务复用次数信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/14 14:36
 */
@Api(value = "数据服务复用次数信息相关服务", tags = "dataReuse")
@RestController
@RequestMapping("${api.version}/dataReuse")
public class DataReuseController extends BaseController {

    private static final Log log = LogFactory.get();
    @Autowired
    private DataReuseService reuseService;

    /**
     * 下载数据服务复用次数文件
     *
     * @return {@link Object}
     */
    @ApiOperation(value = "获取费用次数详情信息", notes = "获取费用次数详情信息")
    @GetMapping("/downloadReuseFile")
    public Object downloadReuseFile(@ApiParam(name = "assetType", value = "服务类型[明细数据，统计分析数据，指标数据]")
                                    @RequestParam(required = false) String assetType) {
        try {
            File file = reuseService.downloadReuseFile(assetType);
            return flushFileBackToUser(file, APPLICATION_OCTET_STREAM_VALUE, file.getName());
        } catch (Exception e) {
            log.error("文件下载失败", e);
        }
        return null;
    }

    @ApiOperation(value = "获取费用次数详情信息", notes = "获取费用次数详情信息")
    @PostMapping("/getDataReuseList")
    public Map<String, Object> getDataReuseList(@ApiParam(name = "assetType", value = "服务类型")
                                                @RequestParam(required = false) String assetType,
                                                @ApiParam(required = true, name = "pageNum", value = "分页编号，从1开始")
                                                @RequestParam(defaultValue = "1") Integer pageNum,
                                                @ApiParam(required = true, name = "pageSize", value = "分页大小")
                                                @RequestParam(defaultValue = "10") Integer pageSize) {
        Map<String, Object> rsMap = this.reuseService.getDataReuseList(assetType, pageNum, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "初始化历史白名单数据", notes = "初始化历史白名单数据")
    @PostMapping("/initHistoryData")
    public Map<String, Object> initHistoryData() {
        this.reuseService.initHistoryData();
        return JsonResult.ok("初始化历史白名单数据异步任务开启成功！");
    }

    @ApiOperation(value = "查询数据服务服务复用次数", notes = "查询数据服务服务复用次数")
    @PostMapping("/querySetDataReuseTotal")
    public Map<String, Object> querySetDataReuseTotal() {
        Map<String, Object> map = reuseService.querySetMartDataApplyTotal();
        return JsonResult.ok().put("data", map);
    }


}
