package com.dhcc.dsp.business.controller;

import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.controller.BaseController;
import com.dhcc.avatar.controller.BaseResponse;
import com.dhcc.avatar.domain.Page;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.dsp.business.model.datasupplement.*;
import com.dhcc.dsp.business.service.datasupplement.*;
import com.dhcc.dsp.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 **/
@Api(value = "Excel导入", tags = "excel")
@RestController
@RequestMapping("/statement/excel")
@CrossOrigin
public class DataSupplementController extends BaseController {

    private static final Log log = LogFactory.get();

    @Autowired
    private IExcelService excelService;

    @Autowired
    private IDataCurdService dataCurdService;

    @Autowired
    private IExcelImportRecordService excelImportRecordService;

    @Autowired
    private IExcelAuthService excelAuthService;

    @Autowired
    private ISimpleTableStoreService simpleTableStoreService;

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "加载Excel文件中的数据到目标数据库表中", httpMethod = "POST", notes = "加载Excel文件中的数据到目标数据库表中")
    @RequestMapping(value = "/loadExcelToTable", method = RequestMethod.POST)
    public BaseResponse loadExcelToTable(
            @ApiParam(required = true, name = "uploadFile", value = "excel文件(.xls,.xlsx)")
            @RequestParam() MultipartFile uploadFile,
            @ApiParam(required = true, name = "tableId", value = "tableId")
            @RequestParam String tableId,
            @ApiParam(required = true, name = "tableDesc", value = "tableDesc")
            @RequestParam String tableDesc,
            @ApiParam(required = true, name = "tableName", value = "tableName")
            @RequestParam String tableName,
            @ApiParam(required = true, name = "addMode", value = "addMode 0代表追加，1代表覆盖")
            @RequestParam Integer addMode) {

        try {
            String fileName = uploadFile.getOriginalFilename();
            if (CommonUtil.testObjectEmpty(fileName) ||
                    !fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
                throw new BusinessException("上传文件格式不正确");
            }
            File file = new File(fileName);
            FileUtils.copyInputStreamToFile(uploadFile.getInputStream(), file);
            ResponseResult responseResult = excelService.loadExcelToTable(file, tableId, addMode, tableDesc, tableName);
            file.delete();
            return BaseResponse.initSuccessBaseResponse(responseResult);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("加载出错", ex);
        }
    }

    @ApiOperation(value = "查询导入数据", httpMethod = "POST", notes = "查询导入的Excel数据")
    @RequestMapping(value = "/selectImportData", method = RequestMethod.POST)
    public BaseResponse selectImportData(
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id,
            @ApiParam(name = "conditions", value = "conditions") @RequestBody List<Condition> conditions,
            @ApiParam(name = "orderTag", value = "orderTag") @RequestParam String orderTag,
            @ApiParam(name = "orderType", value = "orderType") @RequestParam String orderType,
            @ApiParam(name = "pageSize", value = "pageSize") @RequestParam Integer pageSize,
            @ApiParam(name = "pageNum", value = "pageNum") @RequestParam Integer pageNum) {
        try {

            Page page = new Page();
            page.setPageNum(pageNum == null ? 1 : pageNum);
            page.setPageSize(pageSize == null ? 10 : pageSize);

            Data data = dataCurdService.selectImportData(id, conditions, page, orderTag, orderType);
            return BaseResponse.initSuccessBaseResponse(data, page);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询数据出错", ex);
        }
    }

    @ApiOperation(value = "根据条件导出数据或全表导出", httpMethod = "POST", notes = "根据条件导出数据或全表导出")
    @RequestMapping(value = "/exportImportData", method = RequestMethod.POST)
    public BaseResponse exportImportData(
            HttpServletResponse response,
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id,
            @ApiParam(name = "conditions", value = "conditions") @RequestBody List<Condition> conditions,
            @ApiParam(name = "orderTag", value = "orderTag") @RequestParam String orderTag,
            @ApiParam(name = "orderType", value = "orderType") @RequestParam String orderType) {
        response.setHeader("content-type", "application/octet-stream;charset=UTF-8");
        response.setContentType("application/octet-stream;charset=UTF-8");
        byte[] buffer = new byte[1024];
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        OutputStream os = null;
        try {
            String path = dataCurdService.exportImportData(id, conditions, orderTag, orderType);
            String[] split = path.split("/");
            String disposition="attachment;filename=" + java.net.URLEncoder.encode(split[split.length - 1].trim(), "UTF-8");
            String regex = "[`~!@#$%^&*()\\+\\=\\{}|:\"?><【】\\/r\\/n]";
            Pattern pa = Pattern.compile(regex);
            Matcher ma = pa.matcher(disposition);
            if (ma.find()) {
                disposition = ma.replaceAll("");
            }
            response.setHeader("Content-Disposition", disposition);
            path= CommonUtil.cleanPathString(path);
            File file = new File(path);
            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);
            os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }

            file.delete();
            return BaseResponse.initSuccessBaseResponse("下载成功");
        } catch (Exception e) {
            log.error(e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
        }
        return BaseResponse.initErrorBaseResponse("下载失败");
    }

    @ApiOperation(value = "下载重复数据", httpMethod = "POST", notes = "下载重复数据")
    @RequestMapping(value = "/downloadOrRemoveRepeatData", method = RequestMethod.POST)
    public BaseResponse downloadOrRemoveRepeatData(
            HttpServletResponse response,
            @ApiParam(required = true, name = "fileName", value = "fileName") @RequestParam String fileName,
            @ApiParam(required = true, name = "flag", value = "flag") @RequestParam Integer flag
    ) {

        if (flag == 1) {
            File file = new File(fileName);
            file.delete();
            return BaseResponse.initSuccessBaseResponse("删除成功");
        }

        response.setHeader("content-type", "application/octet-stream;charset=UTF-8");
        response.setContentType("application/octet-stream;charset=UTF-8");
        byte[] buffer = new byte[1024];
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        OutputStream os = null;
        try {
            String[] split = fileName.split("/");
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(split[split.length - 1].trim(), "UTF-8"));
            File file = new File(fileName);
            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);
            os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }

            file.delete();
            return BaseResponse.initSuccessBaseResponse("下载成功");
        } catch (Exception e) {
            log.error(e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
        }
        return BaseResponse.initErrorBaseResponse("下载失败");
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除导入的数据", httpMethod = "POST", notes = "删除导入的数据")
    @RequestMapping(value = "/deleteImportData", method = RequestMethod.POST)
    public BaseResponse deleteImportData(
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id,
            @ApiParam(required = true, name = "primaryIds", value = "primaryIds") @RequestBody List<String> primaryIds) {
        try {
            boolean b = dataCurdService.deleteImportData(id, primaryIds);
            return BaseResponse.initSuccessBaseResponse(b);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("删除数据出错", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "根据条件删除数据", httpMethod = "POST", notes = "根据条件删除数据")
    @RequestMapping(value = "/deleteImportDataByCondition", method = RequestMethod.POST)
    public BaseResponse deleteImportDataByCondition(
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id,
            @ApiParam(required = true, name = "conditions", value = "conditions") @RequestBody List<Condition> conditions) {
        try {
            dataCurdService.deleteImportDataByCondition(id, conditions);
            return BaseResponse.initSuccessBaseResponse("删除成功");
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("根据条件删除数据", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "新增或更新导入的数据", httpMethod = "POST", notes = "新增或更新导入的数据")
    @RequestMapping(value = "/insertOrUpdateImportData", method = RequestMethod.POST)
    public BaseResponse insertOrUpdateImportData(
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id,
            @ApiParam(required = true, name = "data", value = "data") @RequestBody List<Map<String, String>> data) {
        try {
            boolean b = dataCurdService.insertOrUpdateImportData(id, data);
            return BaseResponse.initSuccessBaseResponse(b);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("新增或更新数据出错", ex);
        }
    }

    @ApiOperation(value = "查询导入历史的列表", httpMethod = "POST", notes = "查询导入历史的列表")
    @RequestMapping(value = "/selectImportList", method = RequestMethod.POST)
    public BaseResponse selectImportList(
            @ApiParam(name = "tableDesc", value = "tableDesc") @RequestParam(required = false) String tableDesc,
            @ApiParam(name = "pageSize", value = "pageSize") @RequestParam Integer pageSize,
            @ApiParam(name = "pageNum", value = "pageNum") @RequestParam Integer pageNum
    ) {
        try {
            Page page = new Page();
            page.setPageSize(pageSize == null ? 10 : pageSize);
            page.setPageNum(pageNum == null ? 1 : pageNum);

            List<ExcelImportRecordVo> excelImportRecordVos = excelImportRecordService.selectList(tableDesc, page);
            return BaseResponse.initSuccessBaseResponse(excelImportRecordVos, page);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询数据出错", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除导入记录及表", httpMethod = "POST", notes = "删除导入记录及表")
    @RequestMapping(value = "/deleteImportTable", method = RequestMethod.POST)
    public BaseResponse deleteImportTable(
            @ApiParam(required = true, name = "id", value = "id") @RequestParam String id
    ) {
        try {
            return BaseResponse.initSuccessBaseResponse(excelImportRecordService.deleteById(id) == 1);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询数据出错", ex);
        }
    }

    @ApiOperation(value = "判断历史导入的描述信息重名", httpMethod = "POST", notes = "判断历史导入的描述信息重名,true为已重名，false为可以使用")
    @RequestMapping(value = "/checkDescRepeat", method = RequestMethod.POST)
    public BaseResponse checkDescRepeat(@ApiParam(required = true, name = "tableDesc", value = "tableDesc") @RequestParam String tableDesc) {
        try {
            ExcelImportRecord excelImportRecord = excelImportRecordService.selectByTableDesc(tableDesc);
            return BaseResponse.initSuccessBaseResponse(excelImportRecord != null);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询数据出错", ex);
        }
    }

    @ApiOperation(value = "给模板或导入记录授权", httpMethod = "POST", notes = "给模板授权")
    @RequestMapping(value = "/grantAuth", method = RequestMethod.POST)
    public BaseResponse grantTemplate(@ApiParam(required = true, name = "excelAuthList", value = "excelAuthList") @RequestBody List<ExcelAuth> excelAuthList) {
        try {
            boolean b = excelAuthService.grantTemplate(excelAuthList);
            return BaseResponse.initSuccessBaseResponse(b);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("添加权限出错", ex);
        }
    }

    @ApiOperation(value = "查询模板或导入记录的权限列表", httpMethod = "POST", notes = "查看权限")
    @RequestMapping(value = "/queryAuth", method = RequestMethod.POST)
    public BaseResponse queryAuthList(@ApiParam(required = true, name = "id", value = "模板id或者导入记录id") @RequestParam String id) {
        try {
            return BaseResponse.initSuccessBaseResponse(excelAuthService.selectByCorrelationId(id));
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询权限出错", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除模板或导入记录的权限", httpMethod = "POST", notes = "删除权限")
    @RequestMapping(value = "/deleteAuth", method = RequestMethod.POST)
    public BaseResponse delAuth(@ApiParam(required = true, name = "id", value = "权限id") @RequestParam String id) {
        try {
            return BaseResponse.initSuccessBaseResponse(excelAuthService.deleteById(id));
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询权限出错", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "新增或更新模板", httpMethod = "POST", notes = "添加或更新一个新的模板")
    @RequestMapping(value = "/insertOrUpdateTemplate", method = RequestMethod.POST)
    public BaseResponse insertTemplate(
            @ApiParam(required = true, name = "simpleTableStore", value = "simpleTableStore")
            @RequestBody SimpleTableStore simpleTableStore) {
        try {
            simpleTableStoreService.insertOrUpdate(simpleTableStore);
            return BaseResponse.initSuccessBaseResponse(simpleTableStore.getTableId());
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("新增或更新模板列表出错", ex);
        }
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除模板", httpMethod = "POST", notes = "根据tableId删除一个模板")
    @RequestMapping(value = "/deleteTemplate", method = RequestMethod.POST)
    public BaseResponse deleteTemplate(
            @ApiParam(required = true, name = "tableId", value = "tableId")
            @RequestParam String tableId) {
        try {
            return BaseResponse.initSuccessBaseResponse(simpleTableStoreService.delete(tableId) == 1);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("删除模板出错", ex);
        }
    }

    @ApiOperation(value = "查询模板列表", httpMethod = "POST", notes = "根据传入的模板名称模糊查询模板，若模板名称为传递，则查询所有模板")
    @RequestMapping(value = "/selExcelTemplateList", method = RequestMethod.POST)
    public BaseResponse selExcelTemplateList(
            @ApiParam(name = "templateName", value = "templateName") @RequestParam(required = false) String templateName,
            @ApiParam(name = "pageSize", value = "pageSize") @RequestParam(required = false) Integer pageSize,
            @ApiParam(name = "pageNum", value = "pageNum") @RequestParam(required = false) Integer pageNum
    ) {
        try {

            Page page = new Page();
            page.setPageSize(pageSize == null ? 10 : pageSize);
            page.setPageNum(pageNum == null ? 1 : pageNum);

            return BaseResponse.initSuccessBaseResponse(simpleTableStoreService.queryTemplateList(templateName, page), page);
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询模板列表出错", ex);
        }
    }

    @ApiOperation(value = "查询单个模板详情", httpMethod = "POST", notes = "根据模板tableId查询模板详情")
    @RequestMapping(value = "/selExcelTemplateOne", method = RequestMethod.POST)
    public BaseResponse selExcelTemplateOne(
            @ApiParam(required = true, name = "tableId", value = "tableId")
            @RequestParam String tableId) {
        try {
            return BaseResponse.initSuccessBaseResponse(simpleTableStoreService.selectOneTemplate(tableId));
        } catch (Exception ex) {
            return BaseResponse.initErrorBaseResponse("查询模板详情出错", ex);
        }
    }

    @ApiOperation(value = "下载Excel模板", httpMethod = "POST", notes = "下载指定的Excel模板文件")
    @RequestMapping(value = "/downloadExcelTemplate", method = RequestMethod.POST)
    public BaseResponse downloadExcelToTable(HttpServletResponse response,
                                             @ApiParam(required = true, name = "tableId", value = "tableId")
                                             @RequestParam String tableId) {

        String path = simpleTableStoreService.generateExcel(tableId);
        String contentType="application/octet-stream;charset=UTF-8";
        String regex = "[`~!@#$%^&*()\\+\\=\\{}|:\"?><【】\\/r\\/n]";
        Pattern pa = Pattern.compile(regex);
        Matcher ma = pa.matcher(contentType);
        if (ma.find()) {
            contentType = ma.replaceAll("");
        }
        response.setHeader("content-type",contentType);
        response.setContentType("application/octet-stream;charset=UTF-8");
        byte[] buffer = new byte[1024];
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        OutputStream os = null;
        try {
            String[] split = path.split("/");
            String name = split[split.length - 1];
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(name.trim(), "UTF-8"));
            path= CommonUtil.cleanPathString(path);
            File file = new File(path);
            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);
            os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }

            file.delete();
            return BaseResponse.initSuccessBaseResponse("下载成功");
        } catch (Exception e) {
            log.error(e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e);
                }
            }
        }
        return BaseResponse.initErrorBaseResponse("下载失败");
    }
}
