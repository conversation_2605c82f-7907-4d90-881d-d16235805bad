package com.dhcc.dsp.business.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.model.DataSet;
import com.dhcc.dsp.business.model.DataSetSample;
import com.dhcc.dsp.business.model.DataSetWhiteList;
import com.dhcc.dsp.business.service.*;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.consts.AvaConsts;
import com.dhcc.dsp.common.consts.HttpConsts;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import com.dhcc.dsp.common.utils.CustomUtil;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;

@Api(value = "数据视图相关服务调用", tags = "dataset")
@RestController
@RequestMapping("${api.version}/dataSet")
public class DataSetController {

    private static final Log log = LogFactory.get();

    private final DataSetService dataSetService;
    private final DataSetColumnService dataSetColumnService;
    private final DataSetNoColumnService dataSetNoColumnService;
    private final DataSetConditionService dataSetConditionService;
    private final DataSetAuthorityService dataSetAuthorityService;
    private final BaseService baseService;
    private final DataSetWhiteListService dataSetWhiteListService;
    private final OperationService operationService;

    public DataSetController(DataSetService dataSetService, DataSetConditionService dataSetConditionService, DataSetColumnService dataSetColumnService, DataSetNoColumnService dataSetNoColumnService, DataSetAuthorityService dataSetAuthorityService, BaseService baseService, DataSetWhiteListService dataSetWhiteListService, OperationService operationService) {
        this.dataSetService = dataSetService;
        this.dataSetConditionService = dataSetConditionService;
        this.dataSetColumnService = dataSetColumnService;
        this.dataSetNoColumnService = dataSetNoColumnService;
        this.dataSetAuthorityService = dataSetAuthorityService;
        this.baseService = baseService;
        this.dataSetWhiteListService = dataSetWhiteListService;
        this.operationService = operationService;
    }

    @GetMapping("/query")
    public Map<String, Object> queryDataSet(HttpServletRequest request, String siteId, String setSource, String callSource, String name, String simpleName, String describe, String people, String startDate, String endDate, String mode, String type, String setGroup, Integer setStatus, String relationFlag, String orgRang, String analyseObject, Boolean criticalStation, Integer pageNumber, Integer pageSize) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetService.getDataSet(siteId, setSource, orgRang, analyseObject, "", callSource, token, name, simpleName, describe, people, startDate, endDate, null, mode, type, setGroup, setStatus, relationFlag, criticalStation, pageNumber, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    @GetMapping("/getDataSetByName")
    public Map<String, Object> getDataSetByName(String name) {
        List<DataSet> list = dataSetService.getDataSetBySql(null, StrUtil.format("where t1.set_name='{}'", name));
        if (list.size() == 0) {
            return JsonResult.error(StrUtil.format("视图名称【{}】不存在", name));
        } else if (list.size() > 1) {
            return JsonResult.error(StrUtil.format("视图名称【{}】存在重复", name));
        } else {
            return JsonResult.ok().put("data", list.get(0));
        }
    }

    @GetMapping("/getDataSetById")
    public Map<String, Object> getDataSetById(String id) {
        List<DataSet> list = dataSetService.getDataSetBySql(null, StrUtil.format("where t1.id='{}'", id));
        if (list.size() == 0) {
            return JsonResult.error(StrUtil.format("视图ID【{}】不存在", id));
        } else if (list.size() > 1) {
            return JsonResult.error(StrUtil.format("视图ID【{}】存在重复", id));
        } else {
            return JsonResult.ok().put("data", list.get(0));
        }
    }

    @GetMapping("/dataSetExist")
    public Map<String, Object> dataSetExist(String name) {
        List<DataSet> list = dataSetService.getDataSetBySql(null, StrUtil.format("where t1.set_name='{}'", name));
        if (list.size() == 0) {
            return JsonResult.ok().put("data", false);
        } else {
            return JsonResult.ok().put("data", true);
        }
    }

    /**
     * 通过视图id删除数据视图
     *
     * @param dataSetId 数据视图id
     * @return Map<String, Object>
     */
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/deleteById")
    public Map<String, Object> deleteDataSetById(HttpServletRequest request, String dataSetId) {
        String deleteSetName = dataSetService.deleteDataSetById(BaseUtil.getTokenFromHeader(request), dataSetId);
        operationService.saveOperation(dataSetId, OperationUtil.operationType.DELETE, dataSetId, StrUtil.format("数据视图删除:{}", deleteSetName));
        return JsonResult.ok();
    }

    /**
     * 通过视图id查询视图的厂站及测点信息表
     *
     * @param dataSetName 数据视图名称
     * @param pageNumber  页数
     * @param pageSize    条数
     * @return Map<String, Object>
     */
    @GetMapping("/selectDataSetDetail")
    public Map<String, Object> selectDataSetDetail(HttpServletRequest request, String dataSetId, String dataSetName, String timeSeries, Integer pageNumber, Integer pageSize) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetService.getDataSetDetail("", token, dataSetId, dataSetName, "", "", "", timeSeries, "", pageNumber, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    @PostMapping("/selectDataSetDetail")
    public Map<String, Object> selectDataSetDetail(HttpServletRequest request, String dataSetId, String dataSetName, String timeSeries, @RequestBody String condition, Integer pageNumber, Integer pageSize) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetService.getDataSetDetail("", token, dataSetId, dataSetName, "", "", "", timeSeries, condition, pageNumber, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    /**
     * 通过视图id查询视图的结构化数据
     *
     * @param dataSetId  数据视图id
     * @param pageNumber 页数
     * @param pageSize   条数
     * @return Map<String, Object>
     */
    @GetMapping("/selectDataSetColumn")
    public Map<String, Object> selectDataSetColumn(String dataSetId, Integer pageNumber, Integer pageSize) {
        Map<String, Object> rsMap = dataSetColumnService.selectDataSetColumn(dataSetId, pageNumber, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    /**
     * 通过视图id查询视图的非结构化数据
     *
     * @param dataSetId  数据视图id
     * @param pageNumber 页数
     * @param pageSize   条数
     * @return Map<String, Object>
     */
    @GetMapping("/selectDataSetNoColumn")
    public Map<String, Object> selectDataSetNoColumn(HttpServletRequest request, String dataSetId, Integer pageNumber, Integer pageSize) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetNoColumnService.selectDataSetNoColumn(token, dataSetId, pageNumber, pageSize);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    /**
     * 复制数据视图
     *
     * @param dataSetId 视图id
     * @return Map<String, Object>
     */
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/copyDataSet")
    public Map<String, Object> copyDataSet(HttpServletRequest request, String dataSetId, String newDataSetName, Integer newSetStatus, String setType, Integer opType) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetService.copyDataSet(token, dataSetId, newDataSetName, newSetStatus, setType, opType);
        operationService.saveOperation(dataSetId, OperationUtil.operationType.COPY, dataSetId, "数据视图复制");
        return JsonResult.ok();
    }

    /**
     * 通过视图id查询数据视图条件
     *
     * @param dataSetId 数据视图ID
     * @return Map<String, Object>
     */
    @GetMapping("/selectDataSetcondition")
    public Map<String, Object> selectDataSetcondition(String dataSetId) {
        Map<String, Object> rsMap = dataSetConditionService.selectDataSetcondition(dataSetId);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }

    /**
     * 通过数据视图id查询该领域下的所有厂站以及可选条件和必选条件
     *
     * @param dataSetId 数据视图ID
     * @return Map<String, Object>
     */
    @GetMapping("/getSiteInformationBySetId")
    public Map<String, Object> getSiteInformationBySetId(String dataSetId) {
        Map<String, Object> rsMap = dataSetService.getSiteInformationBySetId(dataSetId);
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("siteList", rsMap.get("siteList"))).put("optionalModelCondition", rsMap.get("optionalModelCondition"))).put("requiredModelCondition", rsMap.get("requiredModelCondition"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateDescOfDataSet")
    public Map<String, Object> updateDescOfDataSet(String dataSetId, String dataSetName, String dataSetDesc, String setGroup, Integer serviceType, Integer pushFrequency, String assetType, String labelId, String labelName) {
        dataSetService.updateDescOfDataSet(dataSetId, dataSetName, dataSetDesc, setGroup, serviceType, pushFrequency, assetType, labelId, labelName);
        operationService.saveOperation(dataSetId, OperationUtil.operationType.UPDATE, dataSetId, "数据视图修改");
        return JsonResult.ok();
    }

    /**
     * 通过数据视图id查询数据视图的引用关系
     *
     * @param dataSetId 数据视图ID
     * @return Map<String, Object>
     */
    @GetMapping("/getReferenceById")
    public Map<String, Object> getReferenceById(String dataSetId, Integer page, Integer limit) {
        Map<String, Object> dataMap = dataSetService.getReferenceById(dataSetId, page, limit);
        return Objects.requireNonNull(JsonResult.ok().put("data", dataMap.get("data"))).put("total", dataMap.get("total"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveStationData")
    public Map<String, Object> saveStationData(HttpServletRequest request, String type, String userId, String dataSetName, String dataSetDesc, String fieldType, @RequestBody String params, String setGroup, Boolean deviceObjectModel, Integer serviceType, Integer pushFrequency, String assetType) {
        log.info("开始保存数据视图..............");
        JSONObject paramsJson = JSONUtil.parseObj(params);
        String selectStationData = URLUtil.decode(paramsJson.getStr("selectStationData"));
        String batchId = paramsJson.getStr("batchId");
        String token = BaseUtil.getTokenFromHeader(request);
        userId = AvatarThreadContext.userId.get();
        Map<String, Object> rsMap = dataSetService.saveStationData(type, token, userId, dataSetName, dataSetDesc, fieldType, selectStationData, setGroup, deviceObjectModel, serviceType, pushFrequency, assetType, batchId);
        log.info("数据视图保存完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.SAVE, dataSetName, "数据视图保存");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("setId", rsMap.get("setId"))).put("setName", rsMap.get("setName"))).put("setSimpleName", rsMap.get("setSimpleName"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveModelData")
    public Map<String, Object> saveModelData(HttpServletRequest request, String setGroup, String opType, String userId, String dataSetId, String dataSetName, String dataSetDesc, String fieldType, String batchNumber, String selectDeviceData, String requiredModelCondition, String optionalModelCondition, Integer serviceType, Integer pushFrequency, String assetType) {
        log.info("开始保存模型模式数据视图..............");

        selectDeviceData = URLUtil.decode(selectDeviceData);
        requiredModelCondition = URLUtil.decode(requiredModelCondition);
        optionalModelCondition = URLUtil.decode(optionalModelCondition);

        String token = BaseUtil.getTokenFromHeader(request);
        userId = AvatarThreadContext.userId.get();
        dataSetService.saveModelData(token, setGroup, opType, userId, dataSetId, dataSetName, dataSetDesc, fieldType, batchNumber, selectDeviceData, requiredModelCondition, optionalModelCondition, serviceType, pushFrequency, assetType);
        log.info("模型模式数据视图保存完毕");
        operationService.saveOperation(StrUtil.isBlank(dataSetId) ? UUIDUtil.generateUUID() : dataSetId, OperationUtil.operationType.SAVE, StrUtil.isBlank(dataSetId) ? dataSetName : dataSetId, "模型模式数据视图保存");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveLogicModelData")
    public Map<String, Object> saveLogicModelData(HttpServletRequest request, String type, String opType, @RequestBody String dataViewInfo) {
        log.info("开始保存逻辑模型模式数据视图..............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetService.saveLogicModelData(type, token, opType, dataViewInfo);
        log.info("逻辑模型模式数据视图保存完毕..............");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.SAVE, JSONUtil.toJsonStr(dataViewInfo), "逻辑模型模式数据视图保存");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("setId", rsMap.get("setId"))).put("setName", rsMap.get("setName"))).put("setSimpleName", rsMap.get("setSimpleName"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveStationDataByImport")
    public Map<String, Object> saveStationDataByImport(HttpServletRequest request, @RequestParam MultipartFile file) {
//        String userId = request.getParameter("userId");
        String userId = AvatarThreadContext.userId.get();
        String dataSetName = request.getParameter("dataSetName");
        String dataSetDesc = request.getParameter("dataSetDesc");
        String dataSetId = request.getParameter("dataSetId");//在修改模式下，才会有dataSetId的值
        String setGroup = request.getParameter("setGroup");
        String full = request.getParameter("full");//在修改模式下才起作用
        String deviceObjectModel = request.getParameter("deviceObjectModel");
        Integer serviceType = Convert.toInt(request.getParameter("serviceType"), null);
        Integer pushFrequency = Convert.toInt(request.getParameter("pushFrequency"), null);
        String assetType = request.getParameter("assetType");
        dataSetService.saveStationDataByImport(file, userId, dataSetName, dataSetDesc, dataSetId, full, deviceObjectModel, setGroup, StrUtil.equals(deviceObjectModel, "true") ? null : serviceType, StrUtil.equals(deviceObjectModel, "true") ? null : pushFrequency, assetType);
        log.info("数据视图保存完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.SAVE, "", "测点导入模式数据视图保存");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateStationData")
    public Map<String, Object> updateStationData(HttpServletRequest request, String dataSetId, String dataSetName, String dataSetDesc, @RequestBody String params) {
        log.info("开始更新数据视图..............");
        JSONObject jsonObject = JSONUtil.parseObj(params);
        String selectDeviceData = URLUtil.decode(jsonObject.getStr("selectDeviceData"));
        String selectStationData = URLUtil.decode(jsonObject.getStr("selectStationData"));
        String requiredModelCondition = URLUtil.decode(jsonObject.getStr("requiredModelCondition"));
        String optionalModelCondition = URLUtil.decode(jsonObject.getStr("optionalModelCondition"));
        String batchId = jsonObject.getStr("batchId");

        String token = BaseUtil.getTokenFromHeader(request);
        dataSetService.updateStationData(token, dataSetId, dataSetName, dataSetDesc, selectDeviceData, selectStationData, requiredModelCondition, optionalModelCondition, batchId);
        log.info("数据视图更新完毕");
        operationService.saveOperation(dataSetId, OperationUtil.operationType.UPDATE, dataSetId, "数据视图修改");
        return JsonResult.ok();
    }

    @GetMapping("/importTemplateDownload")
    public void importTemplateDownload(HttpServletResponse response) {
        dataSetService.createImportTemplate(response);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveFilterData")
    public Map<String, Object> saveFilterData(HttpServletRequest request, String setSource, String opType, String userId, String dataSetId, String dataSetName, String dataSetDesc, String filter, String setGroup, Boolean deviceObjectModel, Integer serviceType, Integer pushFrequency, String assetType) {
        log.info("开始保存测点筛选条件视图");
        filter = URLUtil.decode(filter);
        String token = BaseUtil.getTokenFromHeader(request);
        userId = AvatarThreadContext.userId.get();
        Map<String, Object> rsMap = dataSetService.saveFilterData(token, setSource, opType, userId, dataSetId, dataSetName, dataSetDesc, filter, setGroup, deviceObjectModel, serviceType, pushFrequency, assetType);
        log.info("测点筛选条件视图保存完毕");
        operationService.saveOperation(StrUtil.isBlank(dataSetId) ? UUIDUtil.generateUUID() : dataSetId, OperationUtil.operationType.SAVE, StrUtil.isBlank(dataSetId) ? dataSetName : dataSetId, "测点筛选条件数据视图保存");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("setId", rsMap.get("setId"))).put("setName", rsMap.get("setName"))).put("setSimpleName", rsMap.get("setSimpleName"));
    }

    @GetMapping("/exportDataSet")
    public void exportDataSet(HttpServletResponse response, String idList) {
        dataSetService.exportDataSet(response, idList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/importDataSet")
    public Map<String, Object> importDataSet(@RequestParam MultipartFile file, String setGroup) {
        dataSetService.importDataSet(file, setGroup);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, "", "数据视图导入");
        return JsonResult.ok();
    }

    @GetMapping("/getSiteIdFromDataSet")
    public Map<String, Object> getSiteIdFromDataSet(String dataSetName) {
        String siteIds = dataSetService.getSiteIdFromDataSet(dataSetName, "", "", "");
        return JsonResult.ok().put("data", siteIds);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveDataSetAuthority")
    public Map<String, Object> saveDataSetAuthority(HttpServletRequest request, String dataSetId, Integer authCategory, String roleAuthorityList, String orgAuthorityList, String userAuthorityList) {
        log.info("开始保存数据视图权限..............");
        dataSetAuthorityService.saveDataSetAuthority(BaseUtil.getTokenFromHeader(request), dataSetId, authCategory, roleAuthorityList, orgAuthorityList, userAuthorityList);
        log.info("保存数据视图权限完毕");
        return JsonResult.ok();
    }

    @GetMapping("/getDataSetAuthority")
    public Map<String, Object> getDataSetAuthority(String dataSetId, Integer authCategory) {
        Map<String, Object> dataMap = dataSetAuthorityService.getDataSetAuthority(dataSetId, authCategory);
        return JsonResult.ok().put("data", dataMap.get("data"));
    }

    @GetMapping("/getDataSetAuthorityState")
    public Map<String, Object> getDataSetAuthorityState(String dataSetId) {
        Map<String, Object> dataMap = dataSetService.getDataSetAuthorityState(dataSetId);
        return JsonResult.ok().put("data", dataMap.get("data"));
    }

    @GetMapping("/getDataSetVisitUse")
    public Map<String, Object> getDataSetVisitUse(HttpServletRequest request, String dataSetName) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("metaObjName", dataSetName);
        paramMap.put("objType", "DATA_SET");
        JSONObject jsonObject = JSONUtil.parseObj(baseService.doRequest(HttpConsts.METHOD_POST, AvaConsts.QUERY_META_OBJ_VISIT_USER, token, "", paramMap));
        String statusCode = jsonObject.getStr("statusCode");
        String repMessage = jsonObject.getStr("repMessage");
        if (!StrUtil.equals("0", statusCode)) {
            throw new BusinessException("获取视图访问信息出现异常：" + repMessage);
        }
        String result = jsonObject.getByPath("data", String.class);
        ObjectMapper mapper = new ObjectMapper();
        JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HashMap.class);
        List<Map<String, Object>> rsList;
        try {
            rsList = mapper.readValue(result, javaType);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return JsonResult.ok().put("data", rsList);
    }

    @GetMapping("/getDataSetCallCount")
    public Map<String, Object> getDataSetCallCount() {
        Map<String, Object> dataMap = dataSetService.getDataSetCallCount();
        return JsonResult.ok().put("data", dataMap.get("data"));
    }

    @GetMapping("/validateSetName")
    public Map<String, Object> validateSetName(@NotBlank String setName, @NotBlank String dataSetId) {
        String result = dataSetService.validateSetName(setName, dataSetId);
        return JsonResult.ok().put("data", result);
    }

    /**
     * 申请发布视图
     */
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/publishDataSet")
    public Map<String, Object> publishDataSet(HttpServletRequest request, @RequestBody JSONObject dataViewInfo) {
        log.info("开始申请发布视图..............");
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetService.publishDataSet(token, dataViewInfo);
        log.info("申请发布视图完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, JSONUtil.toJsonStr(dataViewInfo), "数据视图申请发布");
        return JsonResult.ok();
    }

    /**
     * 完成发布视图
     */
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/finishPublish")
    public Map<String, Object> finishPublish(HttpServletRequest request, String setId) {
        log.info("开始发布视图..............");
        dataSetService.finishPublish(BaseUtil.getTokenFromHeader(request), setId);
        operationService.saveOperation(setId, OperationUtil.operationType.UPDATE, setId, "数据视图发布");
        log.info("发布视图完毕");
        return JsonResult.ok();
    }

    @PostMapping("/getBusinessKeyById")
    public JsonResult getBusinessKeyById(@NotNull(message = "视图ID不能为空") String id) {
        log.info("开始获取businessKey");
        String businessKey = dataSetService.getBusinessKeyById(id);
        log.info("businessKey获取完毕");
        return JsonResult.ok().put("data", businessKey);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateStatus")
    public JsonResult updateStatus(HttpServletRequest request, @RequestBody String param) {
        dataSetService.updateStatus(BaseUtil.getTokenFromHeader(request), param);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, JSONUtil.toJsonStr(param), "数据视图状态修改");
        return JsonResult.ok();
    }

    @ApiOperation(value = "国能源批量发布功能")
    @PostMapping("/getBusinessKey")
    public JsonResult getBusinessKey(String id) {
        log.info("开始获取businessKey");
        String businessKey = dataSetService.getBusinessKey(id);
        log.info("businessKey获取完毕");
        return JsonResult.ok().put("data", businessKey);
    }

    @ApiOperation(value = "获取白名单列表")
    @GetMapping("/getWhiteList")
    public JsonResult getWhiteList(@NotNull(message = "视图ID不能为空") String setId, String ip, String systemTag) {
        log.info("开始获取白名单列表");
        List<DataSetWhiteList> whiteList = dataSetWhiteListService.getWhiteList(setId, ip, systemTag);
        log.info("获取白名单列表完毕");
        return JsonResult.ok().put("data", whiteList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "保存或更新白名单列表")
    @PostMapping("/saveOrUpdateWhiteList")
    public JsonResult saveOrUpdateWhiteList(@NotNull(message = "视图ID不能为空") String setId, String whiteId, @RequestBody String jsonData) {
        log.info("开始保存或更新白名单列表");
        dataSetWhiteListService.saveOrUpdateWhiteList(setId, whiteId, jsonData);
        log.info("保存或更新白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "批量保存白名单")
    @PostMapping("/saveWhiteListBatch")
    public JsonResult saveWhiteListBatch(@RequestBody String jsonData) {
        log.info("开始批量保存白名单列表");
        dataSetWhiteListService.saveWhiteListBatch(jsonData);
        log.info("批量保存白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "批量导入白名单")
    @PostMapping("/whiteListUpload")
    public JsonResult whiteListUpload(@RequestParam(value = "ips") String ips, @RequestParam(value = "systemTag") String systemTag, @RequestParam(value = "file") MultipartFile file) {
        log.info("开始批量导入白名单列表");
        dataSetWhiteListService.handleUploadFile(CustomUtil.toFile(file), ips, systemTag);
        log.info("批量导入白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除白名单")
    @PostMapping("/deleteByWhiteId")
    public JsonResult deleteByWhiteId(@NotNull(message = "白名单ID不能为空") String whiteId) {
        log.info("开始删除白名单列表");
        dataSetWhiteListService.deleteById(whiteId);
        log.info("删除白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/deleteStationFromDataSet")
    public JsonResult deleteStationFromDataSet(String dataSetId, @RequestBody String stationInfo) {
        log.info("开始删除数据视图中的测点");
        dataSetService.deleteStationFromDataSet(dataSetId, stationInfo);
        log.info("视图测点删除完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/saveSampleData")
    public Map<String, Object> saveSampleData(HttpServletRequest request, String opType, @RequestBody String setSampleInfo) {
        log.info("开始保存样本库模式数据视图..............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSetService.saveSampleData(token, opType, setSampleInfo);
        log.info("样本库模式数据视图保存完毕..............");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.SAVE, JSONUtil.toJsonStr(setSampleInfo), "样本库模式数据视图保存");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("setId", rsMap.get("setId"))).put("setName", rsMap.get("setName"))).put("setSimpleName", rsMap.get("setSimpleName"));
    }

    @GetMapping("/getAllSampleBySetId")
    public Map<String, Object> getAllSampleBySetId(String dataSetId) {
        log.info("开始获取样本数据..............");
        List<DataSetSample> dataSetSampleList = dataSetService.getAllSampleBySetId(dataSetId);
        log.info("样本数据获取完毕..............");
        return JsonResult.ok().put("data", dataSetSampleList);
    }

    @GetMapping("/downloadData")
    public void downloadDataObject(HttpServletRequest request, HttpServletResponse response, String source, String dataObjectName, Integer fileType, boolean convertEnName) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetService.downloadData(response, token, source, dataObjectName, fileType, convertEnName);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "撤回视图审批流程")
    @PostMapping("/withdrawDataSet")
    public JsonResult withdrawDataSet(HttpServletRequest request, @NotBlank(message = "撤回视图ID不能为空") String id) {
        String token = BaseUtil.getTokenFromHeader(request);
        log.info("开始撤回视图审批流程");
        dataSetService.withdrawDataSet(token, id);
        log.info("撤回视图审批流程完毕");
        return JsonResult.ok();
    }

    @PostMapping("/updateDataSetName")
    public JsonResult updateDataSetName(@RequestParam MultipartFile uploadFile) {
        log.info("开始更新视图名称");
        dataSetService.updateDataSetName(uploadFile);
        log.info("视图名称更新完毕");
        return JsonResult.ok();
    }

    @PostMapping("/updateSetCode")
    public JsonResult updateSetCode() {
        log.info("开始更新视图编码");
        dataSetService.updateSetCode();
        log.info("视图编码更新完毕");
        return JsonResult.ok();
    }


    @PostMapping("/changeALLDataSetFeatureStation")
    public JsonResult changeALLDataSetFeatureStation() {
        log.info("开始变更所有关键测点视图数据");
        dataSetService.changeALLDataSetFeatureStation();
        return JsonResult.ok();
    }

    @ApiOperation(value = "设备对象模型批量加入数据集市")
    @PostMapping("/saveDataSetBatch")
    public JsonResult saveDataSetBatch(HttpServletRequest request, @RequestBody String jsonDataStr) {
        log.info("设备对象模型加入数据集市开始");
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetService.saveDataSetBatch(token, jsonDataStr);
        log.info("设备对象模型加入数据集市完毕");
        return JsonResult.ok();
    }

    @ApiOperation(value = "测点数据复制到choose表")
    @PostMapping("/copyDataSetDetailToChoose")
    public JsonResult copyDataSetDetailToChoose(String dataSetId) {
        log.info("测点数据复制到choose表开始");
        Map<String, Object> rsMap = dataSetService.copyDataSetDetailToChoose(dataSetId);
        log.info("测点数据复制到choose表完毕");
        JsonResult ok = JsonResult.ok();
        ok.put("data", rsMap.get("batchId"));
        ok.put("total", rsMap.get("total"));
        return ok;
    }

    @GetMapping("/dataDownloadDataSetList")
    public void dataDownloadDataSetList(HttpServletRequest request, HttpServletResponse response) {
        String siteId = request.getParameter("siteId");
        String setSource = request.getParameter("setSource");
        String orgRang = request.getParameter("orgRang");
        String analyseObject = request.getParameter("analyseObject");
        String ip = BaseUtil.getIpAdrress(request);
        String callSource = request.getParameter("callSource");
        String token = BaseUtil.getTokenFromHeader(request);
        String name = request.getParameter("name");
        String simpleName = request.getParameter("simpleName");
        String describe = request.getParameter("describe");
        String people = request.getParameter("people");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String isPublish = request.getParameter("isPublish");
        String mode = request.getParameter("mode");
        String type = request.getParameter("type");
        String setGroup = request.getParameter("setGroup");
        Integer setStatus = StrUtil.isBlank(request.getParameter("setStatus")) ? null : Convert.toInt(request.getParameter("setStatus"));
        String relationFlag = request.getParameter("relationFlag");
        Boolean criticalStation = StrUtil.isBlank(request.getParameter("criticalStation")) ? null : Convert.toBool(request.getParameter("criticalStation"));
        Integer pageNumber = StrUtil.isBlank(request.getParameter("pageNumber")) ? null : Convert.toInt(request.getParameter("pageNumber"));
        Integer pageSize = StrUtil.isBlank(request.getParameter("pageSize")) ? null : Convert.toInt(request.getParameter("pageSize"));
        try {
            dataSetService.handleDownLoadDataSetList(response, siteId, setSource, orgRang, analyseObject, ip,
                    callSource, token, name, simpleName, describe, people, startDate, endDate, isPublish, mode, type,
                    setGroup, setStatus, relationFlag, criticalStation, pageNumber, pageSize);
        } catch (Exception e) {
            log.error("下载数据出现异常：" + ExceptionUtil.stacktraceToString(e));
            throw new BusinessException(e.getMessage());
        }
    }
}
