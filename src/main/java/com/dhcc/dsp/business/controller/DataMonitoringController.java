package com.dhcc.dsp.business.controller;

import com.dhcc.avatar.controller.BaseController;
import com.dhcc.dsp.business.service.DataMonitoringService;
import com.dhcc.dsp.common.JsonResult;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = "数据服务调用监控", tags = "dataMonitoring")
@RestController
@RequestMapping("${api.version}/dataMonitoring")
public class DataMonitoringController extends BaseController {

    private final DataMonitoringService dataMonitoringService;
    public DataMonitoringController(DataMonitoringService dataMonitoringService) {
        this.dataMonitoringService = dataMonitoringService;
    }

    @GetMapping("/getDataSetMarkCallCount")
    public Map<String, Object> getDataSetMarkCallCount() {
        Map<String, Object> rsMap = dataMonitoringService.getDataSetMarkCallCount();
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @GetMapping("/getDataMonitoringDetailGroupByUser")
    public Map<String, Object> getDataMonitoringDetailGroupByUser(String type, String startTime, String endTime) {
        Map<String, Object> rsMap = dataMonitoringService.getDataMonitoringDetailGroupByUser(type, startTime, endTime);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @GetMapping("/getDataMonitoringDetailGroupByDataId")
    public Map<String, Object> getDataMonitoringDetailGroupByDataId(String type, String startTime, String endTime) {
        Map<String, Object> rsMap = dataMonitoringService.getDataMonitoringDetailGroupByDataId(type, startTime, endTime);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

}
