package com.dhcc.dsp.business.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.dsp.business.model.DataSetGroup;
import com.dhcc.dsp.business.service.DataSetGroupService;
import com.dhcc.dsp.common.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Api(value = "数据视图分组相关服务调用", tags = "dataSetGroup")
@RestController
@RequestMapping("${api.version}/dataSetGroup")
public class DataSetGroupController {

    public static final Log log = LogFactory.get();

    private final DataSetGroupService dataSetGroupService;

    public DataSetGroupController(DataSetGroupService dataSetGroupService) {
        this.dataSetGroupService = dataSetGroupService;
    }

    @ApiOperation(value = "获取分组")
    @GetMapping("/getGroup")
    public JsonResult getGroup(String groupName) {
        log.info("开始获取视图分组");
        List<DataSetGroup> dataSetGroupList = dataSetGroupService.getGroup(groupName);
        log.info("视图分组获取完毕");
        return JsonResult.ok().put("data", dataSetGroupList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "保存分组")
    @PostMapping("/saveGroup")
    public JsonResult saveGroup(@NotBlank(message = "分组名称不能为空") @Length(max = 200, message = "分组名称不能超过200个字符") String name, String pid) {
        log.info("开始保存视图分组");
        String userId = AvatarThreadContext.userId.get();
        String createTime = DateUtil.now();
        dataSetGroupService.saveGroup(name, pid, userId, createTime);
        log.info("视图分组保存完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改分组")
    @PostMapping("/updateGroup")
    public JsonResult updateGroup(@NotBlank(message = "分组名称不能为空") @Length(max = 200, message = "分组名称不能超过200个字符") String name, @NotBlank(message = "分组id不能为空") String groupId) {
        log.info("开始保存视图分组");
        String createTime = DateUtil.now();
        dataSetGroupService.updateGroup(groupId, name, createTime);
        log.info("视图分组保存完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除分组")
    @PostMapping("/deleteGroup")
    public JsonResult deleteGroup(@NotBlank(message = "分组ID不能为空") String groupId) {
        log.info("开始删除视图分组");
        dataSetGroupService.deleteGroup(groupId);
        log.info("视图分组删除完毕");
        return JsonResult.ok();
    }
}
