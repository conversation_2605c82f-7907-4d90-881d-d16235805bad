package com.dhcc.dsp.business.controller;

import cn.hutool.core.util.URLUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.model.DeviceStatus;
import com.dhcc.dsp.business.service.DeviceService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Api(value = "设备相关服务调用", tags = "device")
@RestController
@RequestMapping("${api.version}/device")
public class DeviceController {

    private static final Log log = LogFactory.get();

    private final DeviceService deviceService;

    public DeviceController(DeviceService deviceService) {
        this.deviceService = deviceService;
    }

    @ApiOperation(value = "获取设备信息", notes = "如果deviceId为空，则获取的是第一级的设备，否则是获取当前设备的子设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteId", value = "厂站ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "deviceId", value = "设备ID", dataType = "String"),
            @ApiImplicitParam(name = "access_token", value = "令牌", required = true, dataType = "String")
    })
    @GetMapping("/getDevice")
    public Map<String, Object> getDeviceData(String siteId, String deviceId, HttpServletRequest request) {
        log.info("开始调用系统服务获取设备数据.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = deviceService.getDeviceData(siteId, deviceId, token);
        log.info("设备数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }

    @ApiOperation(value = "获取设备路径树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteList", value = "厂站列表，可为空（返回所有厂站的设备路径树）", required = true, dataType = "String")
    })
    @PostMapping("/getDevicePathTree")
    public Map<String, Object> getDevicePathTreeData(String siteList) {
        log.info("开始调用系统服务获取设备路径数据.............");
        siteList = URLUtil.decode(siteList);
        Map<String, Object> rsMap = deviceService.getDevicePathTreeData(siteList);
        log.info("设备路径数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }

    @PostMapping("/getDeviceTreeByMode")
    public Map<String, Object> getDeviceTreeByMode(HttpServletRequest request, String requiredModelCondition) {
        log.info("开始构造设备树");
        requiredModelCondition = URLUtil.decode(requiredModelCondition);
        String token = BaseUtil.getTokenFromHeader(request);
        String batchNumber = UUIDUtil.generateUUID();
        deviceService.getDeviceTreeByMode(token, batchNumber, requiredModelCondition);

        log.info("设备树构造完毕");
        return JsonResult.ok().put("data", batchNumber);
    }

    @GetMapping("/getDeviceDataList")
    public Map<String, Object> getDeviceDataList(HttpServletRequest request, String siteId, String deviceName, String deviceNum, String treeDeviceNum, Integer page, Integer limit) {
        log.info("开始调用系统服务获取设备数据.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = deviceService.getDeviceDataList(token, siteId, deviceName, deviceNum, treeDeviceNum, page, limit);
        log.info("设备数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList")).put("total", rsMap.get("total"));
    }

    @GetMapping("/getResultDeviceData")
    public Map<String, Object> getResultDeviceData(String batchNumber, String siteId, String deviceNumber) {
        log.info("开始获取模型条件查询结果设备数据.............");
        Map<String, Object> rsMap = deviceService.getResultDeviceData(batchNumber, siteId, deviceNumber);
        log.info("设备数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }

    @GetMapping("/getDeviceStatus")
    public Map<String, Object> getDeviceStatus(String batchNumber) {
        log.info("开始获取模型条件查询结果状态.............");
        DeviceStatus deviceStatus = deviceService.getDeviceStatus(batchNumber);
        log.info("结果状态.获取完毕");
        return JsonResult.ok().put("data", deviceStatus);
    }
}
