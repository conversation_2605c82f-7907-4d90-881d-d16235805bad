package com.dhcc.dsp.business.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.domain.sys.Domain;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.service.BaseService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.consts.ResourceConsts;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import com.dhcc.dsp.system.service.SysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "基础服务调用", tags = "baseService")
@RestController
@RequestMapping("${api.version}/baseService")
public class BaseController {

    public static final Log log = LogFactory.get();

    private final BaseService baseService;

    private final SysConfigService sysConfigService;


    public BaseController(BaseService baseService, SysConfigService sysConfigService) {
        this.baseService = baseService;
        this.sysConfigService = sysConfigService;
    }

    @ApiOperation(value = "基础服务获取业务空间配置的名称和id列表")
    @GetMapping("/getDomainList")
    public Map<String, Object> getDomainList() {
        log.info("开始调用系统服务基础服务获取业务空间配置的名称和id列表.............");
        List<Domain> sysDomainList = baseService.getDomainList();
        log.info("基础服务获取业务空间配置的名称和id列表完毕");
        List<Map<String, Object>> rsList = new ArrayList<>();
        for (Domain domain : sysDomainList) {
            Map<String, Object> m = new HashMap<>();
            m.put("domainId", domain.getDomainid());
            m.put("domainName", domain.getDomainname());
            m.put("isDefault", domain.getIsdfault());
            rsList.add(m);
        }

        return JsonResult.ok().put("data", rsList);
    }

    @GetMapping("/getAvatarUrl")
    public Map<String, Object> getAvatarUrl() {
        String serviceGateway = sysConfigService.getAvatarServiceGateway();
        String context = sysConfigService.getAvatarContext();
        String url = StrUtil.format("{}{}", StrUtil.endWith(serviceGateway, "/") ? serviceGateway : StrUtil.format("{}/", serviceGateway), StrUtil.startWith(context, "/") ? context.substring(1) : context);
        return JsonResult.ok().put("data", url);
    }

    @GetMapping("/getAvatarRedirectUrl")
    public Map<String, Object> getAvatarRedirectUrl() {
        String redirectUrl = sysConfigService.getLogOutRedirectUrl();
        return JsonResult.ok().put("data", redirectUrl);
    }

    @GetMapping("/getAvatarHomeUrl")
    public JsonResult getAvatarHomeUrl() {
        String redirectUrl = sysConfigService.getHomeUrl();
        return JsonResult.ok().put("data", redirectUrl);
    }

    @GetMapping("/getProjectCode")
    public JsonResult getProjectCode() {
        String projectCode = sysConfigService.getProjectCode();
        return JsonResult.ok().put("data", projectCode);
    }

    @GetMapping("/getAvatarContext")
    public Map<String, Object> getAvatarContext() {
        String context = sysConfigService.getAvatarContext();
        if (StrUtil.isNotBlank(context) && !StrUtil.startWithAny(context, "/", "\\")) {
            context = StrUtil.format("/{}", context);
        }
        return JsonResult.ok().put("data", context);
    }

    @GetMapping("/downloadDataObject")
    public void downloadDataObject(HttpServletRequest request, HttpServletResponse response, String dataObjectName, Integer fileType) {
        String token = BaseUtil.getTokenFromHeader(request);
        baseService.downloadDataObject(response, token, dataObjectName, fileType);
    }

    @GetMapping("/getCurrentDbType")
    public Map<String, Object> getCurrentDbType() {
        return JsonResult.ok().put("data", ResourceConsts.DB_TYPE);
    }

    @GetMapping(value = "/logDownload")
    public void logDownload(HttpServletResponse response) {
        try {
            baseService.logDownload(response);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @GetMapping(value = "/getWorkflowSwitch")
    public Map<String, Object> getWorkflowSwitch() {
        String workflowSwitch = sysConfigService.getWorkflowSwitch();
        return JsonResult.ok().put("data", workflowSwitch);
    }

    @GetMapping(value = "/getWorkFlowKey")
    public Map<String, Object> getWorkFlowKey() {
        String workflowKey = sysConfigService.getWorkFlowKey();
        return JsonResult.ok().put("data", workflowKey);
    }

    @GetMapping(value = "/getApplyWorkFlowKey")
    public Map<String, Object> getApplyWorkFlowKey() {
        String workflowKey = sysConfigService.getApplyWorkFlowKey();
        return JsonResult.ok().put("data", workflowKey);
    }

    @GetMapping(value = "/getPermissionSwitch")
    public Map<String, Object> getPermissionSwitch() {
        String workflowKey = sysConfigService.getPermissionSwitch();
        return JsonResult.ok().put("data", workflowKey);
    }

    @GetMapping(value = "/getModuleList")
    public Map<String, Object> getModuleList() {
        String moduleList = sysConfigService.getModuleList();
        return JsonResult.ok().put("data", moduleList);
    }


    @GetMapping(value = "/getPlatform")
    public Map<String, Object> getPlatform() {
        String platform = sysConfigService.getPlatform();
        return JsonResult.ok().put("data", platform);
    }

    @GetMapping(value = "/getAvatarLogoRedirectUrl")
    public Map<String, Object> getAvatarLogoRedirectUrl() {
        String logoRedirect = sysConfigService.getLogoRedirectUrl();
        return JsonResult.ok().put("data", logoRedirect);
    }

    /**
     * 获取环境的厂站id
     */
    @GetMapping(value = "/getEnvironmentSiteId")
    public Map<String, Object> getEnvironmentSiteId() {
        String siteId = sysConfigService.getEnvironmentSiteId();
        return JsonResult.ok().put("data", siteId);
    }


    @GetMapping(value = "/getStationLineExportUrl")
    public Map<String, Object> getStationLineExportUrl() {
        String values = sysConfigService.getStationLineExportUrl();
        return JsonResult.ok().put("data", values);
    }


    @GetMapping(value = "/getFileBlacklist")
    public Map<String, Object> getFileBlacklist() {
        String moduleList = sysConfigService.getFileBlacklist();
        return JsonResult.ok().put("data", moduleList);
    }

    @PostMapping(value = "/getUUID")
    public Map<String, Object> getUUID() {
        return JsonResult.ok().put("data", UUIDUtil.generateUUID());
    }

    @PostMapping("/getQualityDefaultQueryTime")
    public Map<String, Object> getQualityDefaultQueryTime() {
        return JsonResult.ok().put("data", sysConfigService.getQualityDefaultQueryTime());
    }

    @PostMapping("/getFlowSpaceId")
    public Map<String, Object> getFlowSpaceId() {
        return JsonResult.ok().put("data", sysConfigService.getFlowSpaceId());
    }

    @PostMapping("/getSensitiveStationDBName")
    public Map<String, Object> getSensitiveStationDBName() {
        return JsonResult.ok().put("data", sysConfigService.getSensitiveStationDBName());
    }

    @PostMapping("/isQualityAdmin")
    public Map<String, Object> isQualityAdmin() {
        return JsonResult.ok().put("data", sysConfigService.isQualityAdmin());
    }
}
