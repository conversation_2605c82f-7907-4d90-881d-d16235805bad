package com.dhcc.dsp.business.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.model.DataMartWhiteList;
import com.dhcc.dsp.business.service.DataMartAuthorityService;
import com.dhcc.dsp.business.service.DataMartService;
import com.dhcc.dsp.business.service.DataMartWhiteListService;
import com.dhcc.dsp.business.service.OperationService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Api(value = "数据集市相关服务调用", tags = "dataMart")
@RestController
@RequestMapping("${api.version}/dataMart")
public class DataMartController {

    private static final Log log = LogFactory.get();

    private final DataMartService dataMartService;
    private final DataMartWhiteListService dataMartWhiteListService;
    private final DataMartAuthorityService dataMartAuthorityService;
    private final OperationService operationService;

    public DataMartController(DataMartService dataMartService, DataMartWhiteListService dataMartWhiteListService, DataMartAuthorityService dataMartAuthorityService, OperationService operationService) {
        this.dataMartService = dataMartService;
        this.dataMartWhiteListService = dataMartWhiteListService;
        this.dataMartAuthorityService = dataMartAuthorityService;
        this.operationService = operationService;
    }

    @ApiOperation(value = "查询集市列表", notes = "查询集市列表")
    @GetMapping("/query")
    public Map<String, Object> queryDataMart(String martName, String martDesc, String createUser, String useModel, String stime, String etime, String theme, Integer martStatus, Integer page, Integer limit) {
        Map<String, Object> rsMap = dataMartService.getDataMartWithoutDetail("", martName, martDesc, createUser, useModel, stime, etime, theme, martStatus, page, limit);
        return JsonResult.ok().put("total", rsMap.get("total")).put("data", rsMap.get("data"));
    }

    @GetMapping("/queryDetailByName")
    public Map<String, Object> queryDataMartByName(String status, String dataMartName, Integer page, Integer limit) {
        Map<String, Object> rsMap = dataMartService.getDataMart("", status, dataMartName, null, null, null, null, null, null, null, page, limit);
        return JsonResult.ok().put("total", rsMap.get("total")).put("data", rsMap.get("data")).put("dataMartName", dataMartName);
    }

    @ApiOperation(value = "根据ID查询指定集市", notes = "根据ID查询指定集市")
    @GetMapping("/queryDataMartById")
    public Map<String, Object> queryDataMart(String martId) {
        Map<String, Object> rsMap = dataMartService.getDataMartById(martId);
        return JsonResult.ok().put("total", rsMap.get("total")).put("data", rsMap.get("data"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "复制指定集市", notes = "复制指定集市")
    @GetMapping("/copyDataMart")
    public Map<String, Object> copyDataMart(HttpServletRequest request, String martId, String newMartName, Integer newMartStatus, String userName) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataMartService.copyDataMart(token, martId, newMartName, newMartStatus, userName);
        operationService.saveOperation(martId, OperationUtil.operationType.COPY, martId, "数据集市复制");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/deleteById")
    public Map<String, Object> deleteDataMartById(HttpServletRequest request, String dataMartId) {
        String deleteMartName = dataMartService.deleteDataMartById(BaseUtil.getTokenFromHeader(request), dataMartId);
        operationService.saveOperation(dataMartId, OperationUtil.operationType.DELETE, dataMartId, StrUtil.format("数据集市删除:{}", deleteMartName));
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @RequestMapping(value = "/addDataMart", method = RequestMethod.POST)
    public Map<String, Object> addDataMart(String martName, String martDesc, String useModel, String dataSetList, String createUser, String theme, Integer serviceType, Integer pushFrequency) {
        dataSetList = URLUtil.decode(dataSetList);
        dataMartService.addDataMart(martName, martDesc, useModel, dataSetList, createUser, theme, serviceType, pushFrequency);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, "", "数据集市新增");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/editDataMart")
    public Map<String, Object> editDataMarart(String martName, String martDesc, String useModel, String dataSetList, String createUser, String theme, Integer serviceType, Integer pushFrequency) {
        dataSetList = URLUtil.decode(dataSetList);
        dataMartService.editDataMart(martName, martDesc, useModel, dataSetList, createUser, theme, serviceType, pushFrequency);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, martName, "数据集市修改");
        return JsonResult.ok();
    }

    @GetMapping("/getReferenceById")
    public Map<String, Object> getReferenceById(String dataMartId, Integer page, Integer limit) {
        Map<String, Object> dataMap = dataMartService.getReferenceById(dataMartId, page, limit);
        return JsonResult.ok().put("data", dataMap.get("data")).put("total", dataMap.get("total"));
    }

    @GetMapping("/getDataMartPurposeTree")
    public Map<String, Object> getDataMartPurposeTree() {
        Map<String, Object> rsMap = dataMartService.getDataMartPurposeTree();
        return JsonResult.ok().put("total", rsMap.get("total")).put("data", rsMap.get("data"));
    }

    @GetMapping("/getDataMartPurposeAndTheme")
    public Map<String, Object> getDataMartPurposeAndTheme() {
        Map<String, Object> rsMap = dataMartService.getDataMartPurposeAndTheme();
        return JsonResult.ok().put("purposeList", rsMap.get("purposeList")).put("themeList", rsMap.get("themeList"));
    }

    @ApiOperation(value = "国能源批量发布功能")
    @PostMapping("/getBusinessKey")
    public JsonResult getBusinessKey(String id) {
        log.info("开始获取businessKey");
        String businessKey = dataMartService.getBusinessKey(id);
        log.info("businessKey获取完毕");
        return JsonResult.ok().put("data", businessKey);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateStatus")
    public JsonResult updateStatus(@RequestBody String param) {
        log.info("开始更新数据集市状态");
        dataMartService.updateStatus(param);
        log.info("更新数据集市状态完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, JSONUtil.toJsonStr(param), "数据集市状态修改");
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取白名单列表")
    @GetMapping("/getWhiteList")
    public JsonResult getWhiteList(@NotNull(message = "视图ID不能为空") String dataMartId, String ip, String systemTag) {
        log.info("开始获取白名单列表");
        List<DataMartWhiteList> whiteList = dataMartWhiteListService.getWhiteList(dataMartId, ip, systemTag);
        log.info("获取白名单列表完毕");
        return JsonResult.ok().put("data", whiteList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "保存或更新白名单列表")
    @PostMapping("/saveOrUpdateWhiteList")
    public JsonResult saveOrUpdateWhiteList(@NotNull(message = "视图ID不能为空") String dataMartId, String whiteId, String jsonData) {
        log.info("开始保存或更新白名单列表");
        dataMartWhiteListService.saveOrUpdateWhiteList(dataMartId, whiteId, jsonData);
        log.info("保存或更新白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除白名单")
    @PostMapping("/deleteByWhiteId")
    public JsonResult deleteByWhiteId(@NotNull(message = "白名单ID不能为空") String whiteId) {
        log.info("开始删除白名单列表");
        dataMartWhiteListService.deleteById(whiteId);
        log.info("删除白名单列表完毕");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "数据集市授权")
    @PostMapping("/saveDataMartAuthority")
    public Map<String, Object> saveDataMartAuthority(@NotBlank(message = "数据集市id不能为空") String dataMartId, String roleAuthorityList, String orgAuthorityList, String userAuthorityList) {
        log.info("开始保存数据集市权限..............");
        dataMartAuthorityService.saveDataMartAuthority(dataMartId, roleAuthorityList, orgAuthorityList, userAuthorityList);
        log.info("保存数据集市权限完毕");
        return JsonResult.ok();
    }

    @GetMapping("/getDataMartAuthority")
    public Map<String, Object> getDataMartAuthority(@NotBlank(message = "数据集市id不能为空") String dataMartId) {
        Map<String, Object> dataMap = dataMartAuthorityService.getDataMartAuthority(dataMartId);
        return JsonResult.ok().put("data", dataMap.get("data"));
    }

}

