package com.dhcc.dsp.business.controller;

import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.dsp.business.service.SiteService;
import com.dhcc.dsp.common.JsonResult;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = "厂站相关服务调用", tags = "site")
@RestController
@RequestMapping("${api.version}/site")
public class SiteController {

    private static final Log log = LogFactory.get();

    private final SiteService siteService;

    public SiteController(SiteService siteService) {
        this.siteService = siteService;
    }

    @GetMapping("/getSite")
    public Map<String, Object> getSiteData() {
        log.info("开始调用系统服务获取厂站数据");

        Map<String, Object> rsMap = siteService.getSiteData();
        log.info("厂站数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }
}
