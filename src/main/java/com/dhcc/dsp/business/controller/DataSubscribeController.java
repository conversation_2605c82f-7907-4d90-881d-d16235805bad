package com.dhcc.dsp.business.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.service.DataMessageTemplateService;
import com.dhcc.dsp.business.service.DataSubscribeMessageService;
import com.dhcc.dsp.business.service.DataSubscribeService;
import com.dhcc.dsp.business.service.OperationService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "数据订阅相关服务调用", tags = "dataSubscribe")
@RestController
@RequestMapping("${api.version}/dataSubscribe")
public class DataSubscribeController {

    public static final Log log = LogFactory.get();

    private final DataSubscribeService dataSubscribeService;

    private final DataSubscribeMessageService dataSubscribeMessageService;

    private final DataMessageTemplateService dataMessageTemplateService;

    private final OperationService operationService;

    public DataSubscribeController(DataSubscribeService dataSubscribeService, DataSubscribeMessageService dataSubscribeMessageService, DataMessageTemplateService dataMessageTemplateService, OperationService operationService) {
        this.dataSubscribeService = dataSubscribeService;
        this.dataSubscribeMessageService = dataSubscribeMessageService;
        this.dataMessageTemplateService = dataMessageTemplateService;
        this.operationService = operationService;
    }

    @GetMapping("/getDEDirSystem")
    public JsonResult getDEDirSystem(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        List<Map<String, Object>> rsList = dataSubscribeService.getDEDirSystem(token);
        return JsonResult.ok().put("data", rsList);
    }

    @ApiOperation(value = "获取数据订阅列表")
    @GetMapping("/getSubscribe")
    public Map<String, Object> getSubscribe(String name, String fullName, String startTime, String endTime, String userId, Integer page, Integer limit) {
        Map<String, Object> rsMap = dataSubscribeService.getSubscribe(name, fullName, startTime, endTime, userId, page, limit);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }

    @ApiOperation(value = "数据订阅明细")
    @GetMapping("/getSubscribeDetail")
    public Map<String, Object> getSubscribeDetail(String subscribeId) {
        Map<String, Object> rsMap = dataSubscribeService.getSubscribeDetail(subscribeId);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "新增数据订阅")
    @PostMapping("/addSubscribe")
    public Map<String, Object> addSubscribe(HttpServletRequest request, String name, String describe, String userId, String dataList) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataSubscribeService.addSubscribe(token, name, describe, userId, dataList);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, "", "数据订阅新增");
        return JsonResult.ok();
    }


    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改数据订阅")
    @PostMapping("/editSubscribe")
    public Map<String, Object> editSubscribe(HttpServletRequest request, String id, String describe, String dataList) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataSubscribeService.editSubscribe(token, id, describe, dataList);
        operationService.saveOperation(id, OperationUtil.operationType.UPDATE, id, "数据订阅修改");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "更新数据订阅状态")
    @PostMapping("/updateSubscribe")
    public Map<String, Object> updateSubscribe(String id, Integer state) {
        dataSubscribeService.updateSubscribe(id, state);
        operationService.saveOperation(id, OperationUtil.operationType.UPDATE, id, "数据订阅修改");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = " 删除数据订阅")
    @PostMapping("/deleteSubscribe")
    public Map<String, Object> deleteSubscribe(String id) {
        dataSubscribeService.deleteSubscribe(id);
        operationService.saveOperation(id, OperationUtil.operationType.DELETE, id, "数据订阅删除");
        return JsonResult.ok();
    }


    @ApiOperation(value = "所属数据库类型是否支持订阅")
    @PostMapping("/validateDataEntity")
    public Map<String, Object> validateDataEntity(String dataEntityName, String dataCondition) {
        dataSubscribeService.validateDataEntity(dataEntityName, dataCondition);
        return JsonResult.ok();
    }

/*    @ApiOperation(value = "获取数据订阅消息", notes = "")
    @GetMapping("/getDataSubscribeMessage")
    public Map<String, Object> getDataSubscribeMessage(String isRead, String userId, Integer pageNumber, Integer pageSize) {
        Map<String, Object> rsMap = dataSubscribeMessageService.getDataSubscribeMessage(isRead, userId, pageNumber, pageSize);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }*/

    @ApiOperation(value = "获取数据订阅消息")
    @GetMapping("/getDataSubscribeMessage")
    public Map<String, Object> getDataSubscribeMessage(String isRead, String userId, String messageType, String subscribeName
            , String objName, String modelType, String changeType, String changeContent, String startTime, String endTime, String userName, Integer pageNumber, Integer pageSize) {
        Map<String, Object> rsMap = dataSubscribeMessageService.getDataSubscribeMessage(isRead, userId, messageType, subscribeName
                , objName, modelType, changeType, changeContent, startTime, endTime, userName, pageNumber, pageSize);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改数据订阅消息")
    @PostMapping("/updateDataSubscribeMessage")
    public Map<String, Object> updateDataSubscribeMessage(String ids, String isRead) {
        dataSubscribeMessageService.updateDataSubscribeMessage(ids, isRead);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, ids, "数据订阅消息修改");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除数据订阅消息")
    @PostMapping("/deleteDataSubscribeMessage")
    public Map<String, Object> deleteDataSubscribeMessage(String userId, String startTime, String endTime) {
        dataSubscribeMessageService.deleteDataSubscribeMessage(userId, startTime, endTime);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.DELETE, StrUtil.format("{} ~ {}", startTime, endTime), "数据订阅消息删除");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "通过id删除数据订阅消息")
    @PostMapping("/deleteDataSubscribeMessageByIds")
    public Map<String, Object> deleteDataSubscribeMessageByIds(String ids) {
        dataSubscribeMessageService.deleteDataSubscribeMessageByIds(ids);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.DELETE, ids, "数据订阅消息删除");
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取数据订阅消息数量")
    @GetMapping("/getDataSubscribeMessageNum")
    public Map<String, Object> getDataSubscribeMessageNum(String isRead, String userId) {
        Map<String, Object> rsMap = dataSubscribeMessageService.getDataSubscribeMessageNum(isRead, userId);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "获取目录体系")
    @GetMapping("/selectAuditedDEDirSystems")
    public JsonResult selectAuditedDEDirSystems(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSubscribeService.selectAuditedDEDirSystems(token);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "获取目录")
    @GetMapping("/getDEDirFromDirEntry")
    public JsonResult getDEDirFromDirEntry(HttpServletRequest request, String singleTreeId) {
        String token = BaseUtil.getTokenFromHeader(request);
        String result = dataSubscribeService.getDEDirFromDirEntry(token, singleTreeId);
        ObjectMapper mapper = new ObjectMapper();
        JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HashMap.class);
        List<Map<String, Object>> rsList;
        try {
            rsList = mapper.readValue(result, javaType);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return JsonResult.ok().put("data", rsList);
    }

    @ApiOperation(value = "获取资产明细")
    @GetMapping("/queryDataEntitiesUnderDir")
    public JsonResult queryDataEntitiesUnderDir(HttpServletRequest request, String dirSingleTreeId, String dirId, String typeName, String page, String limit) {

        List<Map<String, Object>> rsList = new ArrayList<>();
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSubscribeService.queryDataEntitiesUnderDir(token, dirSingleTreeId, dirId, typeName, page, limit);
        if (rsMap == null || rsMap.size() == 0) {
            return JsonResult.ok().put("data", "").put("total", "0");
        } else {
            String result = rsMap.get("data").toString();
            if (StringUtils.isNotBlank(result)) {
                ObjectMapper mapper = new ObjectMapper();
                JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HashMap.class);
                try {
                    rsList = mapper.readValue(result, javaType);
                } catch (Exception e) {
                    throw new BusinessException(e.getMessage());
                }
            }
            return JsonResult.ok().put("data", rsList).put("total", rsMap.get("total"));
        }

    }

    @ApiOperation(value = "获取采集任务分组列表")
    @GetMapping("/getJobsGroupTree")
    public JsonResult getJobsGroupTree(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSubscribeService.getJobsGroupTree(token);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "获取采集任务")
    @GetMapping("/getJobs")
    public JsonResult getJobs(HttpServletRequest request, String workSpaceID, String groupID, String page, String limit) {
        String token = BaseUtil.getTokenFromHeader(request);
        String result = dataSubscribeService.getJobs(token, workSpaceID, groupID, page, limit);
        ObjectMapper mapper = new ObjectMapper();
        JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HashMap.class);
        List<Map<String, Object>> rsList;
        try {
            rsList = mapper.readValue(result, javaType);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return JsonResult.ok().put("data", rsList);
    }

    @ApiOperation(value = "获取业务建模数据")
    @GetMapping("/queryGroupTree")
    public JsonResult queryGroupTree(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataSubscribeService.queryGroupTree(token);
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "消息模板查询")
    @GetMapping("/getMessageTemplate")
    public Map<String, Object> getMessageTemplate(String id, Integer pageNumber, Integer pageSize) {
        Map<String, Object> rsMap = dataMessageTemplateService.getMessageTemplate(id, pageNumber, pageSize);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "新增消息模板")
    @PostMapping("/addMessageTemplate")
    public Map<String, Object> addMessageTemplate(String templateName, String messageType, String messageTheme, String messageFormat, String createUser) {
        dataMessageTemplateService.addMessageTemplate(templateName, messageType, messageTheme, messageFormat, createUser);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, templateName, "数据订阅消息模板新增");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改消息模板")
    @PostMapping("/updateMessageTemplate")
    public Map<String, Object> updateMessageTemplate(String id, String templateName, String messageType, String messageTheme, String messageFormat, String createUser) {
        dataMessageTemplateService.updateMessageTemplate(id, templateName, messageType, messageTheme, messageFormat, createUser);
        operationService.saveOperation(id, OperationUtil.operationType.UPDATE, id, "数据订阅消息模板修改");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = " 删除消息模板")
    @PostMapping("/deleteMessageTemplate")
    public Map<String, Object> deleteMessageTemplate(String ids) {
        dataMessageTemplateService.deleteMessageTemplate(ids);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.DELETE, ids, "数据订阅消息模板删除");
        return JsonResult.ok();
    }

    @ApiOperation(value = "消息查询")
    @GetMapping("/queryDataSubscribeMessage")
    public Map<String, Object> queryDataSubscribeMessage(String message_type, String name, String content, String userName, Integer pageNumber, Integer pageSize) {
        Map<String, Object> rsMap = dataSubscribeMessageService.queryDataSubscribeMessage(message_type, name, content, userName, pageNumber, pageSize);
        return JsonResult.ok().put("data", rsMap.get("data")).put("total", rsMap.get("total"));
    }

}
