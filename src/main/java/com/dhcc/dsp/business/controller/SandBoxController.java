package com.dhcc.dsp.business.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.dsp.business.service.BaseService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

@Api(value = "数据沙箱相关服务调用", tags = "sandBox")
@RestController
@RequestMapping("${api.version}/sandBox")
public class SandBoxController {

    public static final Log log = LogFactory.get();

    private final BaseService baseService;

    public SandBoxController(BaseService baseService) {
        this.baseService = baseService;
    }

    @PostMapping("/adapter")
    public Map<String, Object> adapterPost(HttpServletRequest request) {
        return sandBoxAdapter(request);
    }

    @GetMapping("/adapter")
    public Map<String, Object> adapterGet(HttpServletRequest request) {
        return sandBoxAdapter(request);
    }

    @DeleteMapping("/adapter")
    public Map<String, Object> adapterDelete(HttpServletRequest request) {
        return sandBoxAdapter(request);
    }

    private Map<String, Object> sandBoxAdapter(HttpServletRequest request) {
        //首先获取参数中需要调用系统的URL值
        String url = request.getParameter("url");
        if (StrUtil.isBlank(url)) {
            return JsonResult.error("参数中的url值为空，请检查");
        }
        //获取参数中的body数据
        BufferedReader reader;
        try {
            reader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
        String body = IoUtil.read(reader);

        //获取参数中的param数据
        Map<String, String[]> requestParamMap = request.getParameterMap();
        Map<String, Object> paramMap = new HashMap<>();
        for (Map.Entry<String, String[]> entry : requestParamMap.entrySet()) {
            String value = entry.getValue()[0];
            paramMap.put(entry.getKey(), value);
        }
        //从系统平台获取返回
        String responseData = baseService.doRequest(request.getMethod(), url, BaseUtil.getTokenFromHeader(request), body, paramMap).toString();

        return JsonResult.ok().put("data", responseData);
    }
}
