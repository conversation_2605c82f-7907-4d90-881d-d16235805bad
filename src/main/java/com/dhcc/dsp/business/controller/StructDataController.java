package com.dhcc.dsp.business.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.service.OperationService;
import com.dhcc.dsp.business.service.StructDataService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

@Api(value = "结构化数据相关服务调用", tags = "structData")
@RestController
@RequestMapping("${api.version}/structData")
public class StructDataController {


    private static final Log log = LogFactory.get();

    private final StructDataService structDataService;

    private final OperationService operationService;

    public StructDataController(StructDataService structDataService, OperationService operationService) {
        this.structDataService = structDataService;
        this.operationService = operationService;
    }

    //    增加一个结构化数据视图
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("saveStructDataSet")
    public Map<String, Object> saveStructDataSet(String userId, String dataSetName, String setSource, String dataSetDesc, String detailInfo, String setGroup, Boolean deviceObjectModel, Integer setStatus, Integer serviceType, Integer pushFrequency, String assetType, String label, String labelName, String assetId) {
        log.info("开始保存结构化数据视图..............");
        detailInfo = URLUtil.decode(detailInfo, CharsetUtil.charset("UTF-8"), false);
        userId = AvatarThreadContext.userId.get();
        Map<String, Object> rsMap = structDataService.saveStructDataSet(userId, dataSetName, setSource, dataSetDesc, detailInfo, setGroup, deviceObjectModel, setStatus, serviceType, pushFrequency, assetType, label, labelName, assetId);
        log.info("结构化数据视图保存完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, dataSetName, "结构化视图新增");
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("setId", rsMap.get("setId"))).put("setName", rsMap.get("setName"))).put("setSimpleName", rsMap.get("setSimpleName"));
    }

    //    修改一个结构化数据视图
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateColumnData")
    public Map<String, Object> updateColumnData(String dataSetId, String dataSetName, String dataSetDesc, String structData) {
        log.info("开始修改结构化数据视图..............");
        structData = URLUtil.decode(structData);
        structDataService.updateColumnData(dataSetId, dataSetName, dataSetDesc, structData);
        log.info("结构化数据视图修改完毕");
        operationService.saveOperation(dataSetId, OperationUtil.operationType.UPDATE, JSONUtil.toJsonStr(structData), "结构化视图修改");
        return JsonResult.ok();
    }

    @PostMapping("/getAuditedDir")
    public Map<String, Object> getAuditedDir(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> structTree = structDataService.getAuditedDir(token);
        return JsonResult.ok().put("data", structTree.get("data"));
    }

    @PostMapping("/getAuditedLeftTree")
    public Map<String, Object> getAuditedLeftTree(HttpServletRequest request, String treeId) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> auditedLeftTree = structDataService.getAuditedLeftTree(token, treeId);
        return JsonResult.ok().put("data", auditedLeftTree.get("data"));
    }

    @PostMapping("/getAuditedObjectData")
    public Map<String, Object> getAuditedObjectData(HttpServletRequest request, String dirId) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> auditedLeftTree = structDataService.getAuditedObjectData(token, dirId);
        return JsonResult.ok().put("data", auditedLeftTree.get("data"));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("updateStructEnData")
    public Map<String, Object> updateStructEnData(String dataSetId) {
        log.info("开始更新结构化数据视图英文信息..............");
        structDataService.updateStructEnData(dataSetId);
        log.info("结构化数据视图英文信息更新完毕");
        return JsonResult.ok();
    }
}
