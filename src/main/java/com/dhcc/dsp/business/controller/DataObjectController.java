package com.dhcc.dsp.business.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.dsp.business.model.DataObject;
import com.dhcc.dsp.business.model.DataObjectGroup;
import com.dhcc.dsp.business.service.DataObjectGroupService;
import com.dhcc.dsp.business.service.DataObjectService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "数据对象相关服务调用", tags = "dataObject")
@RestController
@RequestMapping("${api.version}/dataObject")
public class DataObjectController {

    public static final Log log = LogFactory.get();

    private final DataObjectService dataObjectService;

    private final DataObjectGroupService dataObjectGroupService;

    public DataObjectController(DataObjectService dataObjectService, DataObjectGroupService dataObjectGroupService) {
        this.dataObjectService = dataObjectService;
        this.dataObjectGroupService = dataObjectGroupService;
    }

    @GetMapping("/getMappingColumn")
    public Map<String, Object> getMappingColumn(String name) {
        List<Map<String, Object>> rsList = dataObjectService.getMappingColumn(name);
        return JsonResult.ok().put("data", rsList);
    }

    @GetMapping("/getObjectData")
    public Map<String, Object> getObjectData(HttpServletRequest request, String name, String dataCondition, boolean convertEnName, Integer page, Integer limit) {
        String token = BaseUtil.getTokenFromHeader(request);
        if (StrUtil.isNotBlank(dataCondition)) {
            dataCondition = URLUtil.decode(Base64.decodeStr(dataCondition));
        }
        log.info("接收到的参数：name-【{}】; condition-【{}】", name, dataCondition);
        Map<String, Object> rsMap = dataObjectService.getObjectData(token, name, "", "1", dataCondition, "", "", convertEnName, page, limit, true, null, null, null);
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("data"))).put("total", rsMap.get("total"))).put("metaAttrTypes", rsMap.get("metaAttrTypes"));
    }

    @GetMapping("/getDataObjectGroup")
    public Map<String, Object> getDataObjectGroup(String groupName) {
        List<DataObjectGroup> rsList = dataObjectGroupService.getDataObjectGroup(groupName);
        return JsonResult.ok().put("data", rsList);
    }

    @GetMapping("/getObjectList")
    public Map<String, Object> getObjectList(String groupId) {
        List<DataObject> rsList = dataObjectService.getObjectList(groupId);
        return JsonResult.ok().put("data", rsList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/addObject")
    public Map<String, Object> addObject(HttpServletRequest request, @RequestBody String insertRecords) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataObjectService.addObject(token, insertRecords);
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateObject")
    public Map<String, Object> updateObject(HttpServletRequest request, @RequestBody String updateRecords) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataObjectService.updateObject(token, updateRecords);
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/deleteObject")
    public Map<String, Object> deleteObject(HttpServletRequest request, @RequestBody String deleteRecords) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataObjectService.deleteObject(token, deleteRecords);
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("loadExcelToMetaObj")
    public Map<String, Object> loadExcelToMetaObj(HttpServletRequest request, @RequestParam MultipartFile uploadFile) {
        String token = BaseUtil.getTokenFromHeader(request);
        String metaObjectName = request.getParameter("metaObjectName");
        String sheetIndex = request.getParameter("sheetIndex");
        String startRow = request.getParameter("startRow");
        String endRow = request.getParameter("endRow");
        String startCell = request.getParameter("startCell");
        String endCell = request.getParameter("endCell");
        String columnMapping = request.getParameter("columnMapping");

        dataObjectService.loadExcelToMetaObj(token, uploadFile, metaObjectName, sheetIndex, startRow, endRow, startCell, endCell, columnMapping);

        return JsonResult.ok();
    }

    @GetMapping("/getDWObjectList")
    public Map<String, Object> getDWObjectList(String groupId) {
        List<DataObject> rsList = dataObjectService.getDWObjectList(groupId);
        return JsonResult.ok().put("data", rsList);
    }


    @GetMapping("/getDataLinkMetaObjCount")
    public Map<String, Object> getDataLinkMetaObjCount() {
        Integer count = dataObjectService.getDataLinkMetaObjCount();
        return JsonResult.ok().put("data", count);
    }

    @GetMapping("/getDataLinkMetaObjRowCount")
    public Map<String, Object> getDataLinkMetaObjRowCount(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Long count = dataObjectService.getDataLinkMetaObjRowCount(token);
        return JsonResult.ok().put("data", count);
    }

    @GetMapping("/getDataLinkMetaObjRowCountGNT")
    public Map<String, Object> getDataLinkMetaObjRowCountGNT(HttpServletRequest request) {
        String token = BaseUtil.getTokenFromHeader(request);
        Long count = dataObjectService.getDataLinkMetaObjRowCountGNT(token);
        return JsonResult.ok().put("data", count);
    }
}
