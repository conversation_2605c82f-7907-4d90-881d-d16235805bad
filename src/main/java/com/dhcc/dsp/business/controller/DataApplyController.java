package com.dhcc.dsp.business.controller;

import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.controller.BaseController;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.dsp.business.service.*;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.util.Map;
import java.util.Objects;

import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

@Api(value = "数据申请流程相关服务调用", tags = "dataApply")
@RestController
@RequestMapping("${api.version}/dataApply")
public class DataApplyController extends BaseController {

    private static final Log log = LogFactory.get();
    public static final String DATA = "data";
    public static final String TOTAL = "total";
    public static final String COUNT = "count";

    private final DataApplyService dataApplyService;
    private final DataMonitoringService dataMonitoringService;
    private final DataSetExtendService dataSetExtendService;
    private final DataMonitoringStatService monitoringStatService;

    public DataApplyController(DataApplyService dataApplyService, DataMonitoringService dataMonitoringService, DataReuseService dataReuseService, DataSetExtendService dataSetExtendService, DataMonitoringStatService monitoringStatService) {
        this.dataApplyService = dataApplyService;
        this.dataMonitoringService = dataMonitoringService;
        this.dataSetExtendService = dataSetExtendService;
        this.monitoringStatService = monitoringStatService;
    }

    @ApiOperation(value = "获取任务流程", notes = "获取任务流程")
    @GetMapping("/getTaskTrace")
    public JsonResult getTaskTrace(HttpServletRequest request, @NotBlank(message = "业务唯一ID不能为空") String businessKey) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> map = dataApplyService.getTaskTrace(token, businessKey);
        return JsonResult.ok().put(DATA, map.get(DATA));
    }

    @ApiOperation(value = "获取下一节点信息", notes = "获取下一节点信息")
    @PostMapping("/getNextNodes")
    public Map<String, Object> getNextNodes(HttpServletRequest request, String taskId, String businessKey, String labelId) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> nextNodes = dataApplyService.getNextNodes(token, taskId, businessKey, labelId);
        return JsonResult.ok().put(DATA, nextNodes.get(DATA));
    }

    @ApiOperation(value = "获取人员列表", notes = "获取人员列表")
    @PostMapping("/getUserListByRoleId")
    public Map<String, Object> getUserListByRoleId(HttpServletRequest request, String roleId) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> userList = dataApplyService.getUserListByRoleId(token, roleId);
        return JsonResult.ok().put(DATA, userList.get(DATA));
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "保存申请", notes = "保存申请")
    @PostMapping("/save")
    public Map<String, Object> saveDataApply(HttpServletRequest request, String dataPid, String type, String applyUser, String projectName, String projectManager, String projectBasicInfo, String applyReason, String dept, String oppositeIp) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataApplyService.saveDataApply(token, dataPid, type, applyUser, projectName, projectManager, projectBasicInfo, applyReason, dept, oppositeIp);
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改申请", notes = "修改申请")
    @PostMapping("/update")
    public Map<String, Object> updateDataApply(HttpServletRequest request, String applyId, String dataPid, String type, String applyUser, String projectName, String projectManager, String projectBasicInfo, String applyReason, String dept, String oppositeIp) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataApplyService.updateDataApply(token, applyId, dataPid, type, applyUser, projectName, projectManager, projectBasicInfo, applyReason, dept, oppositeIp);
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "提交申请", notes = "提交申请")
    @PostMapping("/submit")
    public Map<String, Object> submitDataApply(HttpServletRequest request, String applyId, String type, String createUser, String nextUserId, String labelId) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataApplyService.submitDataApply(token, applyId, type, createUser, nextUserId, labelId);
        return JsonResult.ok();
    }

    @ApiOperation(value = "查询申请详情", notes = "查询申请详情")
    @GetMapping("/queryDataApply")
    public Map<String, Object> queryDataApply(String applyId) {
        Map<String, Object> rsMap = dataApplyService.getDataApply(applyId);
        return JsonResult.ok().put(DATA, rsMap.get(DATA));
    }

    @ApiOperation(value = "查询工作流详情", notes = "查询工作流详情")
    @GetMapping("/queryWorkflowDetail")
    public Map<String, Object> queryWorkflowDetail(HttpServletRequest request, @NotBlank(message = "业务唯一ID不能为空") String businessKey) {
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = dataApplyService.queryWorkflowDetail(token, businessKey);
        return Objects.requireNonNull(JsonResult.ok().put(DATA, rsMap.get(DATA))).put("type", rsMap.get("type"));
    }


    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "供工作流调用修改状态接口", notes = "供工作流调用修改状态接口")
    @PostMapping("/changeStatus")
    public Map<String, Object> changeStatus(@RequestBody String params) {
        log.info("接受到参数：" + params);
        dataApplyService.changeStatus(params);
        return JsonResult.ok();
    }


    @ApiOperation(value = "数据服务总数统计", notes = "数据服务总数统计")
    @GetMapping("/queryDataTotal")
    public Map<String, Object> queryDataTotal(String startTime, String endTime) {
        Map<String, Object> rsMap = dataApplyService.getDataTotal(startTime, endTime);
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "数据视图集市服务总数统计", notes = "数据视图集市服务总数统计")
    @GetMapping("/querySetMartDataTotal")
    public Map<String, Object> querySetMartDataTotal(@ApiParam(name = "assetType", value = "服务类型")
                                                     @RequestParam(required = false) String assetType) {
        Map<String, Object> rsMap = dataApplyService.getSetMartDataTotal(assetType);
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "数据服务申请总数统计", notes = "数据服务申请总数统计")
    @GetMapping("/queryDataApplyTotal")
    public Map<String, Object> queryDataApplyTotal(String startTime, String endTime) {
        Map<String, Object> rsMap = dataApplyService.queryDataApplyTotal(startTime, endTime);
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "数据视图集市服务申请总数统计", notes = "数据视图集市服务申请总数统计")
    @GetMapping("/querySetMartDataApplyTotal")
    public Map<String, Object> querySetMartDataApplyTotal() {
        Map<String, Object> rsMap = dataApplyService.querySetMartDataApplyTotal();
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "被重复使用的数据服务个数统计", notes = "被重复使用的数据服务个数统计")
    @GetMapping("/queryDataReuseApplyTotal")
    public Map<String, Object> queryDataReuseApplyTotal() {
        Map<String, Object> rsMap = dataApplyService.queryDataReuseApplyTotal();
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "数据服务复用次数", notes = "数据服务复用次数")
    @GetMapping("/queryDataReuseCount")
    public Map<String, Object> queryDataReuseCount() {
        Map<String, Object> rsMap = dataApplyService.queryDataReuseCount();
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "被使用的数据服务个数统计", notes = "被使用的数据服务个数统计")
    @GetMapping("/queryDataUseApplyTotal")
    public Map<String, Object> queryDataUseApplyTotal() {
        Map<String, Object> rsMap = dataApplyService.queryDataUseApplyTotal();
        return JsonResult.ok().put(DATA, rsMap);
    }

    @ApiOperation(value = "数据服务申请热力图", notes = "数据服务申请热力图")
    @GetMapping("/queryDataApplyHeatMap")
    public Map<String, Object> queryDataApplyHeatMap() {
        Map<String, Object> rsMap = dataApplyService.queryDataApplyHeatMap();
        return JsonResult.ok().put(DATA, rsMap);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "数据视图、集市修改状态接口", notes = "数据视图、集市修改状态接口")
    @PostMapping("/updateStatus")
    public Map<String, Object> updateStatus(HttpServletRequest request, @RequestBody String params) {
        dataApplyService.updateStatus(BaseUtil.getTokenFromHeader(request), params);
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取数据分类型数量")
    @GetMapping("/getDataCountByServiceType")
    public Map<String, Object> getDataMartCountByServiceType() {
        Map<String, Object> rsMap = dataApplyService.getDataCountByServiceType();
        return JsonResult.ok().put(DATA, rsMap.get(DATA));
    }

    @ApiOperation(value = "获取数据分推送频率数量")
    @GetMapping("/getDataCountByPushFrequency")
    public Map<String, Object> getDataMartCountByPushFrequency() {
        Map<String, Object> rsMap = dataApplyService.getDataCountByPushFrequency();
        return JsonResult.ok().put(DATA, rsMap.get(DATA));
    }

    @ApiOperation(value = "获取数据服务使用情况")
    @GetMapping("/queryDataUseMsg")
    public Map<String, Object> queryDataUseMsg() {
        Map<String, Object> rsMap = dataApplyService.queryDataUseMsg();
        return JsonResult.ok().put(DATA, rsMap.get(DATA));
    }

    @ApiOperation(value = "获取数据服务使用排名")
    @GetMapping("/queryDataUseProjectMsg")
    public Map<String, Object> queryDataUseProjectMsg(@ApiParam(name = "assetType", value = "服务类型")
                                                      @RequestParam(required = false) String assetType,
                                                      @ApiParam(name = "pageNum", value = "分页编号，从1开始")
                                                      @RequestParam(required = false) Integer pageNum,
                                                      @ApiParam(name = "pageSize", value = "分页大小")
                                                      @RequestParam(required = false) Integer pageSize) {

        Map<String, Object> rsMap;
        JsonResult ok = JsonResult.ok();
        if (!CommonUtil.testIntegerEmpty(pageNum) && !CommonUtil.testIntegerEmpty(pageSize)) {
            rsMap = dataApplyService.queryDataUseProjectMsg(assetType, pageNum, pageSize);
            ok.put(TOTAL, rsMap.get(TOTAL)).put(DATA, rsMap.get(DATA));
        } else {
            rsMap = dataApplyService.queryDataUseProjectMsg(assetType);
            ok.put(TOTAL, rsMap.get(TOTAL)).put(DATA, rsMap.get(DATA));
            ok.put(COUNT, rsMap.get(COUNT)).put(COUNT, rsMap.get(COUNT));
        }
        return Objects.requireNonNull(ok);
    }

    @ApiOperation(value = "依照月份获取数据服务使用数据")
    @GetMapping("/queryDataUseProjectMsgByMonth")
    public Map<String, Object> queryDataUseProjectMsgByMonth(@ApiParam(name = "assetType", value = "服务类型")
                                                             @RequestParam(required = false) String assetType,
                                                             @ApiParam(name = "monthNumber", value = "月个数，默认返回当年月份")
                                                             @RequestParam(required = false) Integer monthNumber) {
        return dataApplyService.queryDataUseProjectMsgByMonth(assetType, monthNumber);
    }

    @ApiOperation(value = "获取数据服务调用监测明细")
    @GetMapping("/queryMonitoringDataMsg")
    public Map<String, Object> queryMonitoringDataMsg(@ApiParam(name = "assetType", value = "服务类型")
                                                      @RequestParam(required = false) String assetType,
                                                      @ApiParam(name = "dataName", value = "数据服务名称")
                                                      @RequestParam(required = false) String dataName,
                                                      @ApiParam(required = true, name = "pageNum", value = "分页编号，从1开始")
                                                      @RequestParam() Integer pageNum,
                                                      @ApiParam(required = true, name = "pageSize", value = "分页大小")
                                                      @RequestParam() Integer pageSize) {
        JsonResult ok = JsonResult.ok();
        Map<String, Object> rsMap = dataApplyService.queryMonitoringDataMsg(assetType, dataName, pageNum, pageSize);
        ok.put(TOTAL, rsMap.get(TOTAL)).put(DATA, rsMap.get(DATA));
        return Objects.requireNonNull(ok);
    }

    @ApiOperation(value = "处理历史调用记录数据")
    @PostMapping("/handleMonitoringHistoryData")
    public Map<String, Object> handleHistoryData() {
        monitoringStatService.handleHistoryData();
        return JsonResult.ok();
    }

    @ApiOperation(value = "下载数据服务调用监测明细")
    @GetMapping("/downloadMonitoringDataMsg")
    public Object downloadMonitoringDataMsg(@ApiParam(name = "assetType", value = "服务类型")
                                            @RequestParam(required = false) String assetType,
                                            @ApiParam(name = "dataName", value = "数据服务名称")
                                            @RequestParam(required = false) String dataName) {


        try {
            File file = dataApplyService.downloadMonitoringDataMsg(assetType, dataName);
            return flushFileBackToUser(file, APPLICATION_OCTET_STREAM_VALUE, file.getName());
        } catch (Exception e) {
            log.error("文件下载失败", e);
        }
        return null;
    }


    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "更新审批意见（国能投使用）")
    @PostMapping("/updateOpinionByGNT")
    public Map<String, Object> updateOpinionByGNT(@RequestBody String jsonData) {
        dataApplyService.updateOpinionByGNT(jsonData);
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取数据服务监控详情")
    @GetMapping("/getDataMonitoringDetail")
    public Map<String, Object> queryDataUseMsg(@NotBlank(message = "数据类型不能为空") String type,
                                               @NotBlank(message = "数据ID不能为空") String dataId,
                                               Integer page,
                                               Integer limit) {
        Map<String, Object> rsMap = dataMonitoringService.getDataMonitoringDetail(type, dataId, page, limit);
        return Objects.requireNonNull(JsonResult.ok().put(DATA, rsMap.get(DATA))).put(TOTAL, rsMap.get(TOTAL));
    }

    /**
     * 下载数据监控文件
     *
     * @return {@link Object}
     */
    @ApiOperation(value = "下载数据服务监控文件")
    @GetMapping("/downloadMonitoringFile")
    public Object downloadMonitoringFile(@ApiParam(name = "assetType", value = "服务类型")
                                         @RequestParam(required = false) String assetType) {
        try {
            File file = dataApplyService.downloadMonitoringFile(assetType);
            return flushFileBackToUser(file, APPLICATION_OCTET_STREAM_VALUE, file.getName());
        } catch (Exception e) {
            log.error("文件下载失败", e);
        }
        return null;
    }

    @ApiOperation(value = "国能投通过工作流标签获取数据视图数量")
    @GetMapping("/getDataSetCountByLabelId")
    public Map<String, Object> getDataSetCountByLabelId(@NotBlank(message = "工作流标签id不能为空") String labelId) {
        Integer count = dataSetExtendService.getDataSetByLabelId(labelId);
        return JsonResult.ok().put(DATA, count);
    }

    @ApiOperation(value = "国能投查询工作流标签信息")
    @GetMapping("/queryWorkflowLabelInfo")
    public Map<String, Object> queryWorkflowLabelInfo(HttpServletRequest request) {
        Map<String, Object> rsMap = dataApplyService.queryWorkflowLabelInfo(BaseUtil.getTokenFromHeader(request));
        return JsonResult.ok().put(DATA, rsMap.get(DATA));
    }

    @ApiOperation(value = "国能投根据时间查询访问数量")
    @GetMapping("/getVisitCountByTime")
    public Map<String, Object> getVisitCountByMonth(@NotBlank(message = "开始时间不能为空") String startTime, @NotBlank(message = "结束时间不能为空") String endTime) {
        Integer count = dataMonitoringService.getCallNumber("1", startTime, endTime);
        return JsonResult.ok().put(DATA, count);
    }

    @ApiOperation(value = "国能投统计数据服务总数本年、本月，数据服务复用次数本年、本月")
    @GetMapping("/getDataPublishAndReuseCount")
    public Map<String, Object> getDataPublishAndReuseCount() {
        Map<String, Object> rsMap = dataApplyService.getDataPublishAndReuseCount();
        return JsonResult.ok().put(DATA, rsMap);
    }

}

