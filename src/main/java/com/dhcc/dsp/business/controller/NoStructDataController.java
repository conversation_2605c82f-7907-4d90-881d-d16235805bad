package com.dhcc.dsp.business.controller;


import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.model.UnstructuredGroup;
import com.dhcc.dsp.business.service.NoStructDataService;
import com.dhcc.dsp.business.service.OperationService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "非结构化数据相关服务调用", tags = "unStructData")
@RestController
@RequestMapping("${api.version}/noStructData")
public class NoStructDataController {


    private static final Log log = LogFactory.get();

    private final NoStructDataService nostructDataService;

    private final OperationService operationService;

    public NoStructDataController(NoStructDataService nostructDataService, OperationService operationService) {
        this.nostructDataService = nostructDataService;
        this.operationService = operationService;
    }

    @ApiOperation(value = "获取非结构化数据列表")
    @GetMapping("/getDirList")
    public Map<String, Object> getDirList(String parentId,String label) {
        log.info("开始调用系统服务获取非结构化数据列表信息.............");
        List<UnstructuredGroup> dirList = nostructDataService.getDirList(parentId, label);
        log.info("非结构化数据列表业务获取完毕");
        return JsonResult.ok().put("data", dirList);
    }

    @ApiOperation(value = "获取非结构化数据列表所有标签")
    @GetMapping("/getAllTagList")
    public Map<String, Object> getAllTagList(HttpServletRequest request) {
        log.info("开始调用系统服务获取非结构化数据列表所有标签信息.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = nostructDataService.getAllTagList(token);
        log.info("非结构化数据列所有标签信息获取完毕");
        return JsonResult.ok().put("data", rsMap.get("data"));
    }

    @ApiOperation(value = "获取非结构化数据对象列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "searchCondition", value = "查询条件", required = true, dataType = "String")
    })
    @PostMapping("/getNoStructDataList")
    public Map<String, Object> getNoStructDataList(String searchCondition) {
        log.info("开始调用系统服务获取非结构化数据对象列表.............");
        searchCondition = URLUtil.decode(searchCondition);
        //获取非结构化列表，由于系统只有admins权限的用户才能获取到列表，因此这里采用系统的schema用户，这里token设置为空
        Map<String, Object> rsMap = nostructDataService.getNoStructDataList(searchCondition, "");
        log.info("获取非结构化数据对象列表完毕");
        return Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("data"))).put("total", rsMap.get("total"));
    }

    //    增加一个非结构化数据视图
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("saveNoStructDataSet")
    public Map<String, Object> saveNoStructDataSet(String userId, String dataSetName, String dataSetDesc, String fieldType, String NoStructDataInfo, String setGroup, Boolean deviceObjectModel, Integer setStatus, Integer serviceType, Integer pushFrequency, String assetType,String batchId) {
        log.info("开始保存非结构化数据视图..............");
        NoStructDataInfo = URLUtil.decode(NoStructDataInfo);
        userId = AvatarThreadContext.userId.get();
        nostructDataService.saveNoStructDataSet(userId, dataSetName, dataSetDesc, fieldType, NoStructDataInfo, setGroup, deviceObjectModel, setStatus, serviceType, pushFrequency, assetType,batchId);
        log.info("非结构化数据视图保存完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.INSERT, dataSetName, "非结构化视图新增");
        return JsonResult.ok();
    }

    //    修改一个非结构化数据视图
    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @PostMapping("/updateNoColumnData")
    public Map<String, Object> updateNoColumnData(String dataSetId, String dataSetName, String dataSetDesc, String noStructData,String batchId) {
        log.info("开始修改非结构化数据视图..............");
        noStructData = URLUtil.decode(noStructData);
        nostructDataService.updateNoColumnData(dataSetId, dataSetName, dataSetDesc, noStructData,batchId);
        log.info("非结构化数据视图修改完毕");
        operationService.saveOperation(dataSetId, OperationUtil.operationType.UPDATE, JSONUtil.toJsonStr(noStructData), "非结构化视图修改");
        return JsonResult.ok();
    }

    @ApiOperation(value = "非结构化数据复制到choose表")
    @PostMapping("/copyNoStructDataToChoose")
    public JsonResult copyNoStructDataToChoose(String dataSetId) {
        log.info("非结构化数据复制到choose表开始");
        Map<String, Object> rsMap = nostructDataService.copyNoStructDataToChoose(dataSetId);
        log.info("非结构化数据复制到choose表完毕");
        JsonResult ok = JsonResult.ok();
        ok.put("data", rsMap.get("batchId"));
        ok.put("total", rsMap.get("total"));
        return ok;
    }
}
