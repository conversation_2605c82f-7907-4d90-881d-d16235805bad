package com.dhcc.dsp.business.dataquality_new.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.dsp.business.dataquality_new.dto.*;
import com.dhcc.dsp.business.dataquality_new.vo.QualityAlarmHandleHistory;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewAlarmQueryService;
import com.dhcc.dsp.business.dataquality_new.service.SiteListCacheService;
import com.dhcc.dsp.business.service.BaseService;
import com.dhcc.dsp.business.service.DataWarehouseBaseService;
import com.dhcc.dsp.common.consts.HttpConsts;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DataQualityNewAlarmQueryServiceImpl implements DataQualityNewAlarmQueryService {

    private static final Log log = LogFactory.get();

    private final DataWarehouseBaseService dataWarehouseBaseService;
    private final BaseService baseService;
    private final SiteListCacheService siteListCacheService;

    @Override
    public Map<String, List<Entity>> dailyAlarmCount(String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        List<Entity> data = dataWarehouseBaseService.query(null, StrUtil.format("SELECT\n" +
                " t2.id as \"siteId\", t2.site_name as \"siteName\",\n" +
                " t1.alarm_object_type  as \"objectType\",\n" +
                " count(distinct(object_id)) as \"objectCount\",\n" +
                " count(1) as exceptionTimes\n" +
                "FROM\n" +
                " data_quality_alarm t1\n" +
                " LEFT JOIN bcs_sites t2 ON t1.site_id = t2.id \n" +
                "WHERE alarm_time >= '{} 00:00:00' and  alarm_time <= '{} 23:59:59'" +
                "GROUP BY\n" +
                " t2.id,t2.site_name,t1.alarm_object_type ", date, date
        ));
        return data.stream().collect(Collectors.groupingBy(entity -> entity.getStr("siteName")));
    }

    @Override
    public Map<String, List<Entity>> incrementalAlarmCount(String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
        String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
        String sql = StrUtil.format("SELECT\n" +
                "  site_id as \"siteId\",site_name as \"siteName\",\n" +
                "  alarm_object_type as \"objectType\",\n" +
                "  exception_type as \"exceptionType\",\n" +
                "  COUNT (\n" +
                "  DISTINCT ( object_id ))  as \"count\"\n" +
                "FROM\n" +
                "  (\n" +
                "  SELECT\n" +
                "    t1.*,\n" +
                "    (\n" +
                "    CASE\n" +
                "        \n" +
                "        WHEN t1.alarm_object_type = 1 THEN\n" +
                "        (\n" +
                "        SELECT\n" +
                "          t4.site_name \n" +
                "        FROM\n" +
                "          bcs_standard_station t3\n" +
                "          LEFT JOIN bcs_sites t4 ON t3.site_id = t4.ID \n" +
                "        WHERE\n" +
                "          t3.station_number = t1.object_id \n" +
                "          LIMIT 1 \n" +
                "        ) ELSE ( SELECT t4.site_name FROM bcs_sites t4 WHERE SPLIT_PART( t1.object_id, ',', 1 ) = t4.ID ) \n" +
                "      END \n" +
                "      ) AS site_name \n" +
                "    FROM\n" +
                "      data_quality_alarm t1 \n" +
                "    WHERE\n" +
                "      alarm_time >= '{} 00:00:00' \n" +
                "      AND alarm_time <= '{} 23:59:59' \n" +
                "      AND NOT EXISTS (\n" +
                "      SELECT\n" +
                "        1 \n" +
                "      FROM\n" +
                "        data_quality_alarm t2 \n" +
                "      WHERE\n" +
                "        t1.alarm_object_type = t2.alarm_object_type \n" +
                "        AND t1.exception_type = t2.exception_type \n" +
                "        AND t1.object_id = t2.object_id \n" +
                "        AND t2.alarm_time >= '{} 00:00:00' \n" +
                "        AND t2.alarm_time <= '{} 23:59:59' \n" +
                "      )) T \n" +
                "  GROUP BY\n" +
                "    site_id,site_name,\n" +
                "  alarm_object_type,\n" +
                "  exception_type", date, date, offsetDate, offsetEndDate);
        List<Entity> data = dataWarehouseBaseService.query(null, sql);
        return data.stream()
                .filter(entity -> entity.getStr("siteName") != null)
                .collect(Collectors.groupingBy(entity -> entity.getStr("siteName")));
    }

    @Override
    public Map<String, Object> systemInterruptAlarm(String date, String siteId, Integer page, Integer limit) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT\n")
                .append(" t2.site_name AS \"siteName\",\n")
                .append(" SPLIT_PART( t1.object_id, ',', 2 ) AS \"systemName\",\n")
                .append(" t1.start_time AS \"startTime\",\n")
                .append(" t1.end_time AS \"endTime\" \n")
                .append("FROM\n")
                .append(" data_quality_alarm t1\n")
                .append(" LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id \n")
                .append(" WHERE t1.alarm_object_type = 3");
        
        // 如果传入了 siteId 参数，使用 site_id 字段进行过滤
        if (StrUtil.isNotBlank(siteId)) {
            sqlBuilder.append(" AND t1.site_id = '").append(siteId).append("'");
        }
        
        // 如果传入了日期参数，添加日期过滤条件
        if (StrUtil.isNotBlank(date)) {
            sqlBuilder.append(" AND t1.start_time >= '").append(date).append(" 00:00:00'")
                    .append(" AND t1.start_time <= '").append(date).append(" 23:59:59'");
        }
        
        sqlBuilder.append(" ORDER BY t1.start_time DESC");
        
        PageResult<Entity> pageResult = dataWarehouseBaseService.queryWithPage(null, sqlBuilder.toString(), page, limit);
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult);
        return result;
    }

    @Override
    public List<Entity> siteDailyAlarmCount(String siteId, String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        String sql = StrUtil.format("SELECT\n" +
                "  t2.system_name AS \"systemName\",\n" +
                "  COUNT ( 1 ) AS \"alarmTimes\",\n" +
                "  COUNT (DISTINCT ( object_id )) AS \"objectCount\" \n" +
                "FROM\n" +
                "  data_quality_alarm t1 left join bcs_standard_station t2 on t1.object_id = t2.station_number and t2.station_source != '关键测点'\n" +
                "WHERE\n" +
                "  t1.site_id = '{}' and alarm_object_type = 1 and alarm_time >= '{} 00:00:00' and alarm_time <= '{} 23:59:59'\n" +
                "GROUP BY\n" +
                "  \"systemName\"  ", siteId, date, date);
        return dataWarehouseBaseService.query(null, sql);
    }

    @Override
    public Map<String, List<Entity>> siteIncrementalAlarmCount(String siteId, String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
        String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));

        String sql = StrUtil.format("SELECT\n" +
                " t2.system_name AS \"systemName\",\n" +
                " t1.exception_type AS \"exceptionType\",\n" +
                " COUNT ( DISTINCT ( t1.object_id ) ) AS COUNT \n" +
                "FROM\n" +
                " data_quality_alarm t1\n" +
                " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                " AND t1.alarm_object_type = 1 and t2.station_source !='关键测点'\n" +
                "WHERE\n" +
                " t2.site_id = '{}'\n" +
                " AND t1.alarm_time >= '{} 00:00:00' AND t1.alarm_time <= '{} 23:59:59' \n" +
                " AND NOT EXISTS (\n" +
                " SELECT\n" +
                "  1 \n" +
                " FROM\n" +
                "  data_quality_alarm t3 \n" +
                " WHERE\n" +
                "  t1.object_id = t3.object_id \n" +
                "  AND t1.exception_type = t3.exception_type \n" +
                "  AND t3.alarm_object_type = 1 \n" +
                "  AND t3.alarm_time >= '{} 00:00:00' AND t3.alarm_time <= '{} 23:59:59' \n" +
                " ) \n" +
                "GROUP BY\n" +
                " t2.system_name,\n" +
                " t1.exception_type", siteId, date, date, offsetDate, offsetEndDate);
        List<Entity> data = dataWarehouseBaseService.query(null, sql);
        return data.stream().collect(Collectors.groupingBy(entity -> entity.getStr("systemName")));
    }

    @Override
    public List<Entity> siteDailyAlarmDevice(String siteId, String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
        String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
        return dataWarehouseBaseService.query(null, StrUtil.format("SELECT\n" +
                        " t_1.exception_name AS \"exceptionName\",\n" +
                        " t_1.COUNT AS dailyCount,\n" +
                        " t_2.COUNT AS incrementalCount \n" +
                        "FROM\n" +
                        " ( SELECT exception_type AS exception_name, COUNT ( DISTINCT ( object_id )) AS COUNT FROM data_quality_alarm t1 WHERE alarm_object_type = 2 AND t1.alarm_time >= '{} 00:00:00' and t1.alarm_time<= '{} 23:59:59' and SPLIT_PART( t1.object_id, ',', 1 ) = '{}' GROUP BY exception_type ) t_1\n" +
                        " LEFT JOIN ( SELECT exception_type AS exception_name, COUNT ( DISTINCT ( object_id )) AS COUNT FROM data_quality_alarm t1 WHERE alarm_object_type = 2 " +
                        " AND t1.alarm_time >= '{} 00:00:00' and t1.alarm_time <= '{} 23:59:59' " +
                        " AND SPLIT_PART( t1.object_id, ',', 1 ) = '{}' " +
                        " AND not exists (select 1 from data_quality_alarm t2 where t1.object_id = t2.object_id and t1.exception_type = t2.exception_type AND t2.alarm_time >= '{} 00:00:00' and t2.alarm_time <= '{} 23:59:59' and t1.alarm_object_type = 2) GROUP BY exception_type ) t_2 ON t_1.exception_name = t_2.exception_name",
                date, date, siteId, date, date, siteId, offsetDate, offsetEndDate));
    }

    @Override
    public List<Entity> siteDailyAlarmSystem(String siteId, String date) {
        if (StrUtil.isBlank(date)) {
            date = DateUtil.formatDate(new Date());
        }
        String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
        String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
        return dataWarehouseBaseService.query(null, StrUtil.format("SELECT\n" +
                        " t_1.exception_name AS \"exceptionName\",\n" +
                        " t_1.COUNT AS dailyCount,\n" +
                        " t_2.COUNT AS incrementalCount \n" +
                        "FROM\n" +
                        " ( SELECT exception_type AS exception_name, COUNT ( DISTINCT ( object_id )) AS COUNT FROM data_quality_alarm t1 WHERE alarm_object_type = 3 AND t1.start_time >= '{} 00:00:00' and t1.start_time <= '{} 23:59:59' and SPLIT_PART( t1.object_id, ',', 1 ) = '{}' GROUP BY exception_type ) t_1\n" +
                        " LEFT JOIN ( SELECT exception_type AS exception_name, COUNT ( DISTINCT ( object_id )) AS COUNT FROM data_quality_alarm t1 WHERE alarm_object_type = 3 " +
                        " AND t1.alarm_time >= '{} 00:00:00' and t1.alarm_time <= '{} 23:59:59' " +
                        " AND SPLIT_PART( t1.object_id, ',', 1 ) = '{}' " +
                        " AND not exists (select 1 from data_quality_alarm t2 where t1.object_id = t2.object_id and t1.exception_type = t2.exception_type AND t2.alarm_time >= '{} 00:00:00' and t2.alarm_time <= '{} 23:59:59' and t1.alarm_object_type = 3) GROUP BY exception_type ) t_2 ON t_1.exception_name = t_2.exception_name",
                date, date, siteId, date, date, siteId, offsetDate, offsetEndDate));
    }

    private List<JSONObject> getSiteList() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String token = BaseUtil.getTokenFromHeader(request);
        JSONObject siteResult = JSONUtil.parseObj(baseService.doRequest(HttpConsts.METHOD_GET, StrUtil.format("/deviceObject/service/site/getSiteByUsername?username={}", CommonUtil.parameterEnCode(AvatarThreadContext.userName.get())), token, null, new HashMap<>()));
        if (200 != siteResult.getInt("code")) {
            throw new BusinessException("查询厂站信息失败：{}", siteResult.getStr("msg"));
        }
        return siteResult.getJSONArray("data").toList(JSONObject.class);
    }

    @Override
    public Map<String, Object> stationAlarm(QueryStationParamDTO queryStationParamDTO) {
        StringBuilder sql = new StringBuilder();

        // 构建基础查询
        sql.append("SELECT ")
                .append("    t1.id, ")
                .append("    t1.object_id as \"objectId\", ")
                .append("    t1.alarm_info as \"alarmInfo\", ")
                .append("    t2.station_name as \"stationName\", ")
                .append("    t1.alarm_time as \"alarmTime\", ")
                .append("    t1.start_time as \"startTime\", ")
                .append("    t1.end_time as \"endTime\", ")
                .append("    t1.exception_type as \"exceptionType\", ")
                .append("    COALESCE(e.exception_count, 0) AS \"exceptionCount\", ")
                .append("    COALESCE(concat(round(e.total_duration / 60, 2), ' min'), '0 min') AS \"duration\", ")
                .append("    t1.alarm_state as \"alarmState\", ")
                .append("    t2.system_name as \"systemName\", ")
                .append("    t2.physics_device_path as \"physicsDevicePath\", ")
                .append("    t4.site_name as \"siteName\" ")
                .append("FROM (")
                .append("    SELECT * FROM data_quality_alarm ")
                .append("    WHERE alarm_object_type = 1 ");

        // 添加时间条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getStartTime())) {
            sql.append("    AND alarm_time >= '")
                    .append(DateUtil.parse(queryStationParamDTO.getStartTime()).toStringDefaultTimeZone())
                    .append("' ");
        }
        if (StrUtil.isNotBlank(queryStationParamDTO.getEndTime())) {
            sql.append("    AND alarm_time <= '")
                    .append(DateUtil.parse(queryStationParamDTO.getEndTime()).toStringDefaultTimeZone())
                    .append(".999' ");
        }

        // 添加告警状态条件
        if (queryStationParamDTO.getAlarmState() != null) {
            sql.append("    AND alarm_state = ")
                    .append(queryStationParamDTO.getAlarmState());
        }

        // 添加异常类型条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getExceptionName())) {
            sql.append("    AND exception_type LIKE '%")
                    .append(queryStationParamDTO.getExceptionName())
                    .append("%' ");
        }

        // 添加告警ID条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getAlarmId())) {
            sql.append("    AND id = '")
                    .append(queryStationParamDTO.getAlarmId())
                    .append("' ");
        }

        sql.append(") t1 ");

        // 关联站点表
        sql.append("JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number ")
                .append("    AND t2.station_source != '关键测点' ");

        // 添加站点ID条件 - 这里添加了站点ID过滤
        if (StrUtil.isNotBlank(queryStationParamDTO.getSiteId())) {
            sql.append("    AND t2.site_id = '")
                    .append(queryStationParamDTO.getSiteId())
                    .append("' ");
        }

        // 添加站点条件
        List<JSONObject> siteList = getSiteList();
        if (!siteList.isEmpty()) {
            String siteIds = siteList.stream()
                    .map(site -> "'" + site.getStr("id") + "'")
                    .collect(Collectors.joining(","));
            sql.append("    AND t2.site_id IN (").append(siteIds).append(") ");
        }

        // 添加系统名称条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getSystemName())) {
            sql.append("    AND t2.system_name = '")
                    .append(queryStationParamDTO.getSystemName())
                    .append("' ");
        }

        // 添加站点名称条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getStationName())) {
            sql.append("    AND t2.station_name LIKE '%")
                    .append(queryStationParamDTO.getStationName())
                    .append("%' ");
        }

        // 添加实体化设备路径条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getPath())) {
            sql.append("    AND (t2.physics_device_path = '")
                    .append(queryStationParamDTO.getPath())
                    .append("' OR t2.physics_device_path LIKE '")
                    .append(queryStationParamDTO.getPath())
                    .append("~%') ");
        }

        // 关联sites表
        sql.append("LEFT JOIN bcs_sites t4 ON t2.site_id = t4.id ");

        // 添加告警统计子查询
        sql.append("LEFT JOIN (")
                .append("    SELECT ")
                .append("        t5.alarm_id, ")
                .append("        COUNT(DISTINCT t5.exception_id) as exception_count, ")
                .append("        SUM(COALESCE(t6.duration, 0)) as total_duration ")
                .append("    FROM data_quality_alarm_exception t5 ")
                .append("    LEFT JOIN data_quality_exception t6 ON t5.exception_id = t6.ID ");

        // 如果有时间条件，在子查询中也添加过滤
        sql.append("    WHERE t5.alarm_id IN (")
                .append("        SELECT id FROM data_quality_alarm ")
                .append("        WHERE alarm_object_type = 1 ");

        if (StrUtil.isNotBlank(queryStationParamDTO.getStartTime())) {
            sql.append("        AND alarm_time >= '")
                    .append(DateUtil.parse(queryStationParamDTO.getStartTime()).toStringDefaultTimeZone())
                    .append("' ");
        }
        if (StrUtil.isNotBlank(queryStationParamDTO.getEndTime())) {
            sql.append("        AND alarm_time <= '")
                    .append(DateUtil.parse(queryStationParamDTO.getEndTime()).toStringDefaultTimeZone())
                    .append(".999' ");
        }

        sql.append("    ) ")
                .append("    GROUP BY t5.alarm_id ")
                .append(") e ON t1.id = e.alarm_id ");

        // 添加订阅条件
        if (Boolean.TRUE.equals(queryStationParamDTO.getSubscription())) {
            sql.append("WHERE EXISTS (")
                    .append("    SELECT 1 FROM data_quality_alarm_subscription t_s ")
                    .append("    WHERE t1.object_id = t_s.object_id ")
                    .append("    AND t_s.object_type = t1.alarm_object_type ")
                    .append("    AND t_s.user_id = '")
                    .append(AvatarThreadContext.userId.get())
                    .append("' ) ");
        }

        // 添加增量查询条件
        if (Boolean.TRUE.equals(queryStationParamDTO.getIncremental())) {
            String date = DateUtil.formatDate(new Date());
            String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
            String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));

            sql.append(sql.indexOf("WHERE") > 0 ? " AND " : " WHERE ")
                    .append("(t1.alarm_time >= '").append(date).append(" 00:00:00' ")
                    .append("AND t1.alarm_time <= '").append(date).append(" 23:59:59' ")
                    .append(" AND NOT EXISTS (")
                    .append("    SELECT 1 FROM data_quality_alarm t_a ")
                    .append("    WHERE t1.object_id = t_a.object_id ")
                    .append("    AND t1.exception_type = t_a.exception_type ")
                    .append("    AND t_a.alarm_object_type = 1 ")
                    .append("    AND t_a.alarm_time >= '").append(offsetDate).append(" 00:00:00' ")
                    .append("    AND t_a.alarm_time <= '").append(offsetEndDate).append(" 23:59:59'")
                    .append(" )) ");
        }

        // 添加工单条件
        if (StrUtil.isNotBlank(queryStationParamDTO.getWoId())) {
            sql.append(sql.indexOf("WHERE") > 0 ? " AND " : " WHERE ")
                    .append("EXISTS (")
                    .append("    SELECT 1 FROM data_quality_wo_alarm wo ")
                    .append("    WHERE wo.alarm_id = t1.id ")
                    .append("    AND wo.wo_id = '")
                    .append(queryStationParamDTO.getWoId())
                    .append("') ");
        }

        // 添加排序
        sql.append(" ORDER BY t1.alarm_time DESC");

        // 执行分页查询
        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(
                null,
                sql.toString(),
                queryStationParamDTO.getPage(),
                queryStationParamDTO.getLimit()
        );

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public Map<String, Object> deviceAlarm(QueryDeviceParamDTO queryDeviceParamDTO) {
        StringJoiner condition = new StringJoiner(" AND ");
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getSiteId())) {
            condition.add("t2.site_id = '" + queryDeviceParamDTO.getSiteId() + "'");
        }
        List<JSONObject> siteList = getSiteList();
        String siteIds = siteList.stream().map(site -> StrUtil.format("'{}'", site.getStr("id"))).collect(Collectors.joining(","));
        condition.add(StrUtil.format("t2.site_id in ({})", siteIds));
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getPath())) {
            condition.add(StrUtil.format("(t2.path = '{}' or t2.path like '{}~%')", queryDeviceParamDTO.getPath(), queryDeviceParamDTO.getPath()));
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getDeviceName())) {
            condition.add("t2.name = '" + queryDeviceParamDTO.getDeviceName() + "'");
        }
        if (queryDeviceParamDTO.getAlarmState() != null) {
            condition.add("t1.alarm_state = " + queryDeviceParamDTO.getAlarmState());
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getExceptionName())) {
            condition.add("t1.exception_type like '%" + queryDeviceParamDTO.getExceptionName() + "%'");
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getAlarmTime())) {
            condition.add(StrUtil.format("t1.alarm_time >= '{} 00:00:00' and t1.alarm_time <= '{} 23:59:59'", queryDeviceParamDTO.getAlarmTime(), queryDeviceParamDTO.getAlarmTime()));
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getStartTime())) {
            condition.add("t1.alarm_time >= '" + queryDeviceParamDTO.getStartTime() + "'");
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getEndTime())) {
            condition.add("t1.alarm_time <= '" + queryDeviceParamDTO.getEndTime() + "'");
        }
        if (StrUtil.isNotBlank(queryDeviceParamDTO.getAlarmId())) {
            condition.add("t1.id = '" + queryDeviceParamDTO.getAlarmId() + "'");
        }
        if (Boolean.TRUE.equals(queryDeviceParamDTO.getSubscription())) {
            condition.add("EXISTS (\n" +
                    "SELECT\n" +
                    "1 \n" +
                    "FROM\n" +
                    "data_quality_alarm_subscription t_s \n" +
                    "WHERE\n" +
                    "t1.object_id = t_s.object_id \n" +
                    "AND t_s.object_type = t1.alarm_object_type \n" +
                    "AND t_s.user_id = '" + AvatarThreadContext.userId.get() + "' \n" +
                    ")");
        }
        if (Boolean.TRUE.equals(queryDeviceParamDTO.getIncremental())) {
            String date = DateUtil.formatDate(new Date());
            String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
            String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
            condition.add(StrUtil.format("(t1.alarm_time >= '{} 00:00:00' AND t1.alarm_time <= '{} 23:59:59' \n" +
                    " AND NOT EXISTS (\n" +
                    " SELECT\n" +
                    "  1 \n" +
                    " FROM\n" +
                    "  data_quality_alarm t_a \n" +
                    " WHERE\n" +
                    "  t1.object_id = t_a.object_id \n" +
                    "  AND t1.exception_type = t_a.exception_type \n" +
                    "  AND t_a.alarm_object_type = 2 \n" +
                    "  AND t_a.alarm_time >= '{} 00:00:00' AND t_a.alarm_time <= '{} 23:59:59' \n" +
                    " )) \n", date, date, offsetDate, offsetEndDate));
        }
        String sql = "SELECT\n" +
                " t1.id,\n" +
                " t1.object_id as \"objectId\",\n" +
                " t1.alarm_info as \"alarmInfo\",\n" +
                " t1.alarm_time AS \"alarmTime\",\n" +
                " t2.NAME AS \"deviceName\",\n" +
                " t1.exception_type AS \"exceptionType\",\n" +
                " ( SELECT COUNT ( distinct(exception_id) ) FROM data_quality_alarm_exception t4 WHERE t1.ID = t4.alarm_id ) AS \"exceptionCount\",\n" +
                " (SELECT concat(round(SUM( t5.duration ) / 60,2),' min') \n" +
                " FROM\n" +
                "  data_quality_alarm_exception t4\n" +
                "  LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.ID \n" +
                " WHERE\n" +
                "  t4.alarm_id = t1.ID \n" +
                " ) AS \"duration\",\n" +
                " t1.alarm_state AS \"alarmState\",\n" +
                " t1.start_time AS \"startTime\",\n" +
                " t1.end_time AS \"endTime\",\n" +
                " t3.site_name AS \"siteName\" \n" +
                "FROM\n" +
                " data_quality_alarm t1\n" +
                " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code \n" +
                " AND t1.alarm_object_type = 2\n" +
                " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID \n" +
                "WHERE\n" +
                " t1.alarm_object_type = 2 " + (condition.length() > 0 ? " AND " + condition : "") + " order by t1.alarm_time desc ";

        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(null, sql, queryDeviceParamDTO.getPage(), queryDeviceParamDTO.getLimit());
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public Map<String, Object> systemAlarm(QuerySystemDTO querySystemDTO) {
        StringJoiner condition = new StringJoiner(" AND ");
        if (StrUtil.isNotBlank(querySystemDTO.getSiteId())) {
            condition.add("t2.id = '" + querySystemDTO.getSiteId() + "'");
        }
        if (StrUtil.isNotBlank(querySystemDTO.getWoId())) {
            condition.add("EXISTS (SELECT 1 FROM data_quality_wo_alarm wo WHERE wo.alarm_id = t1.id AND wo.wo_id = '" + querySystemDTO.getWoId() + "')");
        }
        List<JSONObject> siteList = getSiteList();
        String siteIds = siteList.stream().map(site -> StrUtil.format("'{}'", site.getStr("id"))).collect(Collectors.joining(","));
        condition.add(StrUtil.format("t2.id in ({})", siteIds));
        if (StrUtil.isNotBlank(querySystemDTO.getSystemName())) {
            condition.add("SPLIT_PART( t1.object_id, ',', 2 ) = '" + querySystemDTO.getSystemName() + "'");
        }
        if (querySystemDTO.getAlarmState() != null) {
            condition.add("t1.alarm_state = " + querySystemDTO.getAlarmState());
        }
        if (StrUtil.isNotBlank(querySystemDTO.getExceptionName())) {
            condition.add("t1.exception_type like '%" + querySystemDTO.getExceptionName() + "%'");
        }
        if (StrUtil.isNotBlank(querySystemDTO.getAlarmTime())) {
            condition.add(StrUtil.format("t1.alarm_time >= '{} 00:00:00' and t1.alarm_time <= '{} 23:59:59'", querySystemDTO.getAlarmTime(), querySystemDTO.getAlarmTime()));
        }
        if (StrUtil.isNotBlank(querySystemDTO.getStartTime())) {
            try {
                // 验证是否为有效的日期格式
                DateTime startDateTime = DateUtil.parse(querySystemDTO.getStartTime());
                condition.add("t1.alarm_time >= '" + startDateTime.toStringDefaultTimeZone() + "'");
            } catch (Exception e) {
                // 记录日志并跳过无效的日期参数
                log.warn("Invalid startTime format: {}, skipping startTime condition", querySystemDTO.getStartTime());
            }
        }
        if (StrUtil.isNotBlank(querySystemDTO.getEndTime())) {
            try {
                // 验证是否为有效的日期格式
                DateTime endDateTime = DateUtil.parse(querySystemDTO.getEndTime());
                condition.add("t1.alarm_time <= '" + endDateTime.toStringDefaultTimeZone() + ".999'");
            } catch (Exception e) {
                // 记录日志并跳过无效的日期参数
                log.warn("Invalid endTime format: {}, skipping endTime condition", querySystemDTO.getEndTime());
            }
        }
        if (StrUtil.isNotBlank(querySystemDTO.getAlarmId())) {
            condition.add("t1.id = '" + querySystemDTO.getAlarmId() + "'");
        }
        if (Boolean.TRUE.equals(querySystemDTO.getSubscription())) {
            condition.add("EXISTS (\n" +
                    "SELECT\n" +
                    "1 \n" +
                    "FROM\n" +
                    "data_quality_alarm_subscription t_s \n" +
                    "WHERE\n" +
                    "t1.object_id = t_s.object_id \n" +
                    "AND t_s.object_type = t1.alarm_object_type \n" +
                    "AND t_s.user_id = '" + AvatarThreadContext.userId.get() + "' \n" +
                    ")");
        }
        if (Boolean.TRUE.equals(querySystemDTO.getIncremental())) {
            String date = DateUtil.formatDate(new Date());
            String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
            String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
            condition.add(StrUtil.format("(t1.alarm_time >= '{} 00:00:00' AND t1.alarm_time <= '{} 23:59:59' \n" +
                    " AND NOT EXISTS (\n" +
                    " SELECT\n" +
                    "  1 \n" +
                    " FROM\n" +
                    "  data_quality_alarm t_a \n" +
                    " WHERE\n" +
                    "  t1.object_id = t_a.object_id \n" +
                    "  AND t1.exception_type = t_a.exception_type \n" +
                    "  AND t_a.alarm_object_type = 3 \n" +
                    "  AND t_a.alarm_time >= '{} 00:00:00' AND t_a.alarm_time <= '{} 23:59:59' \n" +
                    " )) \n", date, date, offsetDate, offsetEndDate));
        }
        String sql = "SELECT\n" +
                " t1.id,\n" +
                " t1.object_id as \"objectId\",\n" +
                " t1.alarm_info as \"alarmInfo\",\n" +
                " t1.alarm_time AS \"alarmTime\",\n" +
                " t1.alarm_state AS \"alarmState\",\n" +
                " t1.start_time AS \"startTime\",\n" +
                " t1.end_time AS \"endTime\",\n" +
                " SPLIT_PART( t1.object_id, ',', 2 ) as \"systemName\",\n" +
                " t1.exception_type AS \"exceptionType\",\n" +
                " ( SELECT COUNT ( distinct(exception_id) ) FROM data_quality_alarm_exception t4 WHERE t1.id = t4.alarm_id ) AS \"exceptionCount\",\n" +
                " (SELECT concat(round(SUM( t5.duration ) / 60,2),' min') \n" +
                " FROM\n" +
                "  data_quality_alarm_exception t4\n" +
                "  LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id \n" +
                " WHERE\n" +
                "  t4.alarm_id = t1.id \n" +
                " ) AS \"duration\",\n" +
                " t2.site_name AS \"siteName\" \n" +
                "FROM\n" +
                " data_quality_alarm t1\n" +
                " LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id \n" +
                " AND t1.alarm_object_type = 3\n" +
                "where t1.alarm_object_type = 3 " + (condition.length() > 0 ? " AND " + condition : "") + " order by t1.alarm_time desc ";

        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(null, sql, querySystemDTO.getPage(), querySystemDTO.getLimit());
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public Map<String, Object> siteAlarm(QuerySiteDTO querySiteDTO) {
        StringJoiner condition = new StringJoiner(" AND ");
        if (StrUtil.isNotBlank(querySiteDTO.getSiteId())) {
            condition.add("t2.id = '" + querySiteDTO.getSiteId() + "'");
        }
        if (StrUtil.isNotBlank(querySiteDTO.getWoId())) {
            condition.add("EXISTS (SELECT 1 FROM data_quality_wo_alarm wo WHERE wo.alarm_id = t1.id AND wo.wo_id = '" + querySiteDTO.getWoId() + "')");
        }
        List<JSONObject> siteList = getSiteList();
        String siteIds = siteList.stream().map(site -> StrUtil.format("'{}'", site.getStr("id"))).collect(Collectors.joining(","));
        condition.add(StrUtil.format("t2.id in ({})", siteIds));
        if (querySiteDTO.getAlarmState() != null) {
            condition.add("t1.alarm_state = " + querySiteDTO.getAlarmState());
        }
        if (StrUtil.isNotBlank(querySiteDTO.getExceptionName())) {
            condition.add("t1.exception_type like '%" + querySiteDTO.getExceptionName() + "%'");
        }
        if (StrUtil.isNotBlank(querySiteDTO.getAlarmTime())) {
            condition.add(StrUtil.format("t1.alarm_time >= '{} 00:00:00' and t1.alarm_time <= '{} 23:59:59'", querySiteDTO.getAlarmTime(), querySiteDTO.getAlarmTime()));
        }
        if (StrUtil.isNotBlank(querySiteDTO.getStartTime())) {
            condition.add("t1.alarm_time >= '" + DateUtil.parse(querySiteDTO.getStartTime()).toStringDefaultTimeZone() + "'");
        }
        if (StrUtil.isNotBlank(querySiteDTO.getEndTime())) {
            condition.add("t1.alarm_time <= '" + DateUtil.parse(querySiteDTO.getEndTime()).toStringDefaultTimeZone() + ".999'");
        }
        if (StrUtil.isNotBlank(querySiteDTO.getAlarmId())) {
            condition.add("t1.id = '" + querySiteDTO.getAlarmId() + "'");
        }
        if (Boolean.TRUE.equals(querySiteDTO.getSubscription())) {
            condition.add("EXISTS (\n" +
                    "SELECT\n" +
                    "1 \n" +
                    "FROM\n" +
                    "data_quality_alarm_subscription t_s \n" +
                    "WHERE\n" +
                    "t1.object_id = t_s.object_id \n" +
                    "AND t_s.object_type = t1.alarm_object_type \n" +
                    "AND t_s.user_id = '" + AvatarThreadContext.userId.get() + "' \n" +
                    ")");
        }
        if (Boolean.TRUE.equals(querySiteDTO.getIncremental())) {
            String date = DateUtil.formatDate(new Date());
            String offsetDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -3));
            String offsetEndDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(date), -1));
            condition.add(StrUtil.format("(t1.alarm_time >= '{} 00:00:00' AND t1.alarm_time <= '{} 23:59:59' \n" +
                    " AND NOT EXISTS (\n" +
                    " SELECT\n" +
                    "  1 \n" +
                    " FROM\n" +
                    "  data_quality_alarm t_a \n" +
                    " WHERE\n" +
                    "  t1.object_id = t_a.object_id \n" +
                    "  AND t1.exception_type = t_a.exception_type \n" +
                    "  AND t_a.alarm_object_type = 4 \n" +
                    "  AND t_a.alarm_time >= '{} 00:00:00' AND t_a.alarm_time <= '{} 23:59:59' \n" +
                    " )) \n", date, date, offsetDate, offsetEndDate));
        }

        // 优化后的SQL - 使用LEFT JOIN替代子查询
        String sql = "SELECT\n" +
                " t1.id,\n" +
                " t1.object_id as \"objectId\",\n" +
                " t1.alarm_info as \"alarmInfo\",\n" +
                " t1.alarm_time AS \"alarmTime\",\n" +
                " t1.alarm_state AS \"alarmState\",\n" +
                " t1.start_time AS \"startTime\",\n" +
                " t1.end_time AS \"endTime\",\n" +
                " t1.exception_type AS \"exceptionType\",\n" +
                " COALESCE(t3.exceptionCount, 0) AS \"exceptionCount\",\n" +
                " COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS \"duration\",\n" +
                " t2.site_name AS \"siteName\" \n" +
                "FROM\n" +
                " data_quality_alarm t1\n" +
                " LEFT JOIN bcs_sites t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.id \n" +
                " AND t1.alarm_object_type = 4\n" +
                " LEFT JOIN (\n" +
                "   SELECT \n" +
                "     t4.alarm_id,\n" +
                "     COUNT(DISTINCT t4.exception_id) as exceptionCount,\n" +
                "     SUM(COALESCE(t5.duration, 0)) as totalDuration\n" +
                "   FROM data_quality_alarm_exception t4\n" +
                "   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id\n" +
                "   GROUP BY t4.alarm_id\n" +
                " ) t3 ON t1.id = t3.alarm_id\n" +
                "WHERE t1.alarm_object_type = 4" + (condition.length() > 0 ? " AND " + condition : "") + " \n" +
                "ORDER BY t1.alarm_time DESC ";

        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(null, sql, querySiteDTO.getPage(), querySiteDTO.getLimit());
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public Map<String, Object> relatedException(QueryExceptionDTO queryExceptionDTO) {
        String sql = "SELECT\n" +
                "exception_type AS \"exceptionType\",\n" +
                "start_time AS \"startTIme\",\n" +
                "end_time AS \"endTime\" \n" +
                "FROM\n" +
                "data_quality_exception t1 \n" +
                "WHERE\n" +
                "EXISTS (\n" +
                "SELECT\n" +
                "1 \n" +
                "FROM\n" +
                "data_quality_alarm_exception t2\n" +
                "LEFT JOIN data_quality_alarm t3 ON t2.alarm_id = t3.id WHERE t2.exception_id = t1.id \n" + (StrUtil.isBlank(queryExceptionDTO.getAlarmId()) ? "" : "AND t2.alarm_id in (" + BaseUtil.surroundWithSingleQuotes(queryExceptionDTO.getAlarmId()) + ")") +
                ") " + (StrUtil.isBlank(queryExceptionDTO.getExceptionType()) ? "" : "AND t1.exception_type = '" + queryExceptionDTO.getExceptionType() + "'") + " order by start_time desc ";
        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(null, sql, queryExceptionDTO.getPage(), queryExceptionDTO.getLimit());
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public void handleAlarm(HandleAlarmDTO handleAlarmDTO) {
        if (CollUtil.isEmpty(handleAlarmDTO.getAlarmIds())) {
            throw new BusinessException("告警id不能为空");
        }
        if (handleAlarmDTO.getHandleState() == null) {
            throw new BusinessException("处理状态不能为空");
        }
        String handleTime = DateUtil.now();
        List<QualityAlarmHandleHistory> handleHistoryList = new ArrayList<>();
        for (String alarmId : handleAlarmDTO.getAlarmIds()) {
            QualityAlarmHandleHistory handleHistory = new QualityAlarmHandleHistory();
            handleHistory.setId(IdUtil.simpleUUID());
            handleHistory.setAlarmId(alarmId);
            handleHistory.setHandleTime(handleTime);
            handleHistory.setHandleState(handleAlarmDTO.getHandleState());
            handleHistory.setHandleUserId(AvatarThreadContext.userId.get());
            handleHistory.setHandleDetail(handleAlarmDTO.getHandleDetail());
            handleHistory.setHandleUserName(AvatarThreadContext.userName.get());
            handleHistoryList.add(handleHistory);
        }
        List<List<Object>> insertList = handleHistoryList.stream().map(item -> {
            List<Object> list = new ArrayList<>();
            list.add(item.getId());
            list.add(item.getAlarmId());
            list.add(item.getHandleTime());
            list.add(item.getHandleState());
            list.add(item.getHandleUserId());
            list.add(item.getHandleDetail());
            list.add(item.getHandleUserName());
            return list;
        }).collect(Collectors.toList());
        dataWarehouseBaseService.batchInsert("insert into data_quality_alarm_handle_history(id,alarm_id,handle_time,handle_state,handle_user_id,handle_detail,handle_user_name) values(?,?,?,?,?,?,?)", insertList);
        String alarmIds = handleAlarmDTO.getAlarmIds().stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
        dataWarehouseBaseService.update(StrUtil.format("update data_quality_alarm set alarm_state = {} where id in ({})", handleAlarmDTO.getHandleState(), alarmIds));
        dataWarehouseBaseService.update(StrUtil.format("update data_quality_exception set handle_state = {} where id in (select exception_id from data_quality_alarm_exception where alarm_id in ({}))", handleAlarmDTO.getHandleState(), alarmIds));
    }

    @Override
    public Map<String, Object> getHandleHistory(String alarmId, Integer page, Integer limit) {
        String sql = "SELECT\n" +
                "handle_time AS hendleTime,\n" +
                "handle_user_name AS handleUserName,\n" +
                "handle_detail AS handleDetail \n" +
                "FROM\n" +
                "data_quality_alarm_handle_history \n" +
                "WHERE\n" +
                "alarm_id = '" + alarmId + "' \n" +
                "ORDER BY\n" +
                "handle_time DESC";
        PageResult<Entity> data = dataWarehouseBaseService.queryWithPage(null, sql, page, limit);
        Map<String, Object> result = new HashMap<>();
        result.put("total", data.getTotal());
        result.put("data", data);
        return result;
    }

    @Override
    public void subscriptionAlarm(String alarmId) {
        List<Entity> alarmById = dataWarehouseBaseService.query(null, "select * from data_quality_alarm where id = '" + alarmId + "'");
        if (CollUtil.isNotEmpty(alarmById)) {
            //订阅表新增一条记录
            Entity alarm = alarmById.get(0);
            List<List<Object>> insertList = Arrays.asList(Arrays.asList(IdUtil.simpleUUID(), AvatarThreadContext.userId.get(), alarm.get("object_id"), alarm.get("alarm_object_type")));
            dataWarehouseBaseService.batchInsert("insert into data_quality_alarm_subscription(id,user_id,object_id,object_type) values(?,?,?,?)", insertList);
        }
    }

    @Override
    public Map<String, Object> getExceptionSummary(List<String> alarmIds) {
        if (CollUtil.isEmpty(alarmIds)) {
            throw new BusinessException("告警id不能为空");
        }
        List<Entity> data = dataWarehouseBaseService.query(null,
                StrUtil.format("SELECT\n" +
                        " count(distinct(object_id)) as \"objectCount\",\n" +
                        " count(1) as \"count\",\n" +
                        " concat(round(sum(duration)/60,2),' min') as \"duration\"\n" +
                        "FROM\n" +
                        "data_quality_exception t1 \n" +
                        "WHERE\n" +
                        "EXISTS (\n" +
                        "SELECT\n" +
                        "1 \n" +
                        "FROM\n" +
                        "data_quality_alarm_exception t2\n" +
                        "LEFT JOIN data_quality_alarm t3 ON t2.alarm_id = t3.id \n" +
                        "WHERE\n" +
                        "t3.id IN ( {} ) \n" +
                        "AND t2.exception_id = t1.id)", alarmIds.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","))));
        return data.get(0);
    }

    @Override
    public Map<String, Object> getByException(String exceptionId) {
        String sql = "SELECT\n" +
                " t1.id,\n" +
                " t1.object_id as \"objectId\",\n" +
                " t1.alarm_info as \"alarmInfo\",\n" +
                " t1.alarm_time AS \"alarmTime\",\n" +
                " t1.alarm_state AS \"alarmState\",\n" +
                " t1.start_time AS \"startTime\",\n" +
                " t1.end_time AS \"endTime\",\n" +
                " t1.exception_type AS \"exceptionType\"\n" +
                "FROM\n" +
                "data_quality_alarm t1 \n" +
                "WHERE\n" +
                "EXISTS (\n" +
                "SELECT\n" +
                "1 \n" +
                "FROM\n" +
                "data_quality_exception t2\n" +
                "INNER JOIN data_quality_alarm_exception t3 ON t2.id = t3.exception_id \n" +
                "WHERE\n" +
                "t2.id = '" + exceptionId + "'\n" +
                "AND t1.id = t3.alarm_id )";
        List<Entity> dataList = dataWarehouseBaseService.query(null, sql);
        if (CollUtil.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return new HashMap<>();
    }
}
