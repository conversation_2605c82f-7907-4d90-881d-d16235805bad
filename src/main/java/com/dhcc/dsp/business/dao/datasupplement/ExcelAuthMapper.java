package com.dhcc.dsp.business.dao.datasupplement;

import com.dhcc.dsp.business.model.datasupplement.ExcelAuth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 作者：chenhao
 * 日期：2/3/21 5:52 PM
 **/
public interface ExcelAuthMapper {

    int insert(ExcelAuth excelAuth);

    List<ExcelAuth> selectByCorrelationId(String correlationId);

    boolean deleteById(String id);

    ExcelAuth selectOneById(String id);

    ExcelAuth selectOneByCorrelationIdAndUserId(@Param("correlationId") String correlationId, @Param("userId") String userId);

}
