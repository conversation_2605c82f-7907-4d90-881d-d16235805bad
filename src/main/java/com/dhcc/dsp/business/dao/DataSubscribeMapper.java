package com.dhcc.dsp.business.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dhcc.dsp.business.model.DataSubscribe;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface DataSubscribeMapper extends BaseMapper<DataSubscribe> {

    @Select("select resource.id2 from ddaas_resource resource, ddaas_metaobject metaobject where resource.id=metaobject.resourceid and metaobject.name=${condition}")
    List<Map<String, String>> checkingToDdaasResource(@Param("condition") String condition);

    @Select("select DISTINCT user_id from edp_data_subscribe where state = '0'")
    List<String> getUserId();

    @Select("select s.id as ID, s.subscribe_name as SUBSCRIBENAME, s.subscribe_desc as SUBSCRIBEDESC,u.username as USERNAME, u.fullname as FULLNAME, s.create_time as CREATETIME, s.state as STATE\n" +
            "from edp_data_subscribe s, ddaas_users u\n" +
            "where s.user_id=u.userid\n" +
            "${condition}")
    List<Map<String, Object>> getSubscribe(Page page, @Param("condition") String condition);

    @Select("SELECT\n" +
            "\ta.business_key BUSINESSKEY,\n" +
            "\ta.`status` STATUS,\n" +
            "\ta.task_id TASKID,\n" +
            "\ta.id APPLYID,\n" +
            "\ts.id AS ID,\n" +
            "\ts.subscribe_name AS SUBSCRIBENAME,\n" +
            "\ts.subscribe_desc AS SUBSCRIBEDESC,\n" +
            "\tu.username AS USERNAME,\n" +
            "\tu.fullname AS FULLNAME,\n" +
            "\ts.create_time AS CREATETIME,\n" +
            "\ts.state AS STATE \n" +
            "FROM\n" +
            "\tedp_data_subscribe s LEFT JOIN edp_data_apply a on s.id = a.data_pid INNER JOIN\n" +
            "\tddaas_users u on s.user_id = u.userid\n" +
            "${condition}")
    List<Map<String, Object>> getSubscribeWithWorkflow(Page page, @Param("condition") String condition, @Param("applyUserId") String applyUserId);


    @Select("select distinct ur.userid\n" +
            "from ddaas_authobject auth, ddaas_role_auth ra, ddaas_users_roles ur\n" +
            "where auth.authobjectid=ra.authobjectid and ra.object_id=ur.roleid\n" +
            "and auth.authobjecturl='category=dataService~permission=subscribeAdmin'")
    List<String> getSubscribeAdminUserIds();

    @Select("select detail.obj_id\n" +
            "from edp_data_subscribe subscribe, edp_data_subscribe_detail detail\n" +
            "where subscribe.id = detail.subscribe_id\n" +
            "and subscribe.user_id = '${userId}'")
    List<String> getDetailObjIdsByUserId(@Param("userId") String userId);
}
