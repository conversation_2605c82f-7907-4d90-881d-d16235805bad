package com.dhcc.dsp.business.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dhcc.dsp.business.model.Station;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface StationMapper extends BaseMapper<Station> {

    @Select("select attr_classify as ABBRCLASSIFY , count(*) as NUM from (select distinct attr_classify, cn_name, en_name from bcs_standard_station where site_id in (${siteId}) and logic_path='${devicePath}' ${conditionStrs}) rs group by attr_classify")
    List<Map<String, Object>> getAbbrClassifyNum(@Param("siteId") String siteId, @Param("devicePath") String devicePath, @Param("conditionStrs") String conditionStrs);

    @Select("select distinct attr_classify as ABBRCLASSIFY, cn_name as BCSCHNAME, en_name as BCSENNA<PERSON> from bcs_standard_station where site_id in (${siteId}) and logic_path='${devicePath}' and attr_classify='${abbrClassify}' ${conditionStrs}")
    List<Map<String, Object>> getAbbrClassify(Page<Map<String, Object>> page, @Param("siteId") String siteId, @Param("devicePath") String devicePath, @Param("abbrClassify") String abbrClassify, @Param("conditionStrs") String conditionStrs);

    @Select("select station_number as STATIONNUMBER , station_code as SITECODE , station_global_number as STATIONGLOBALNUMBER , pointtype as POINTTYPE  from data_service.all_station   where   ${queryStr} ")
    List<Map<String, Object>> getStationGlobalNumber(@Param("queryStr") String queryStr);

}
