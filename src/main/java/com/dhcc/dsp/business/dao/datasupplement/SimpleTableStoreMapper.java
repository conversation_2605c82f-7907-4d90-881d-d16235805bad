package com.dhcc.dsp.business.dao.datasupplement;

import com.dhcc.dsp.business.model.datasupplement.SimpleTableStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SimpleTableStoreMapper {

    int deleteByPrimaryKey(String tableId);

    int insert(SimpleTableStore record);

    SimpleTableStore selectByPrimaryKey(String tableId);

    int updateByPrimaryKey(SimpleTableStore record);

    List<SimpleTableStore> queryTableList(@Param("siteId") String siteId, @Param("machineSet") String machineSet, @Param("type") String type);

    List<SimpleTableStore> queryTemplateList(@Param("machineSet") String machineSet, @Param("type") String type, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    List<SimpleTableStore> selTemplateList(@Param("machineSet") String machineSet, @Param("type") String type);

    int queryTemplateCount(String type);
}