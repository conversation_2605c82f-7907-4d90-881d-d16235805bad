package com.dhcc.dsp.business.dao.datasupplement;

import com.dhcc.dsp.business.model.datasupplement.ExcelImportRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExcelImportRecordMapper {

    List<ExcelImportRecord> selectList(@Param("tableDesc") String tableDesc);

    int queryPageCount(@Param("tableDesc") String tableDesc);

    ExcelImportRecord selectById(String id);

    int insert(ExcelImportRecord excelImportRecord);

    int deleteById(String id);

    ExcelImportRecord selectByTableDesc(String tableDesc);

    ExcelImportRecord selectByResourceIdAndTableName(@Param("resourceId") String resourceId, @Param("tableName") String tableName);

    int deleteByResourceAndTableName(@Param("resourceId") String resourceId, @Param("tableName") String tableName);
}