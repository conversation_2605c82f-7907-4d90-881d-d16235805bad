package com.dhcc.dsp.business.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dhcc.dsp.business.model.TimeSeriesData;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TimeSeriesDataMapper extends BaseMapper<TimeSeriesData> {

    @Select("SELECT *\n" +
            "FROM time_series_${siteId} AS A \n" +
            "WHERE data_time = (\n" +
            " SELECT MAX( B.data_time ) \n" +
            " FROM time_series_${siteId} AS B \n" +
            " WHERE A.station_global_number = B.station_global_number \n" +
            "   AND B.station_global_number in (${codes}))")
    List<TimeSeriesData> getLatestData(@Param("siteId") String siteId, @Param("codes") String codes);

    @Select("SELECT * FROM time_series_${siteId} WHERE station_global_number in (${codes}) AND data_time BETWEEN '${startTime}' AND '${endTime}'")
    @Options(fetchSize = 10000)
    List<TimeSeriesData> getData(@Param("siteId") String siteId, @Param("codes") String codes, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
