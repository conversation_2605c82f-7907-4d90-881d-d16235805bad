# SQL性能优化建议

## 问题分析

原始SQL存在以下性能问题：

1. **子查询性能瓶颈**：每行数据都要执行两个子查询
2. **函数调用无法使用索引**：`SPLIT_PART(t1.object_id, ',', 1)`
3. **大表JOIN性能问题**：5000万+2000万+163万条数据的关联

## 优化方案

### 1. 代码层面优化（已实施）

将子查询改为LEFT JOIN，减少重复计算：

```sql
-- 优化前（慢）
SELECT 
  ( SELECT COUNT(distinct(exception_id)) FROM data_quality_alarm_exception t4 WHERE t1.id = t4.alarm_id ) AS "exceptionCount",
  (SELECT concat(round(SUM(t5.duration)/60,2),' min') 
   FROM data_quality_alarm_exception t4
   LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id 
   WHERE t4.alarm_id = t1.id 
  ) AS "duration"
FROM data_quality_alarm t1

-- 优化后（快）
SELECT 
  COALESCE(t3.exceptionCount, 0) AS "exceptionCount",
  COALESCE(CONCAT(ROUND(t3.totalDuration / 60, 2), ' min'), '0 min') AS "duration"
FROM data_quality_alarm t1
LEFT JOIN (
  SELECT 
    t4.alarm_id,
    COUNT(DISTINCT t4.exception_id) as exceptionCount,
    SUM(COALESCE(t5.duration, 0)) as totalDuration
  FROM data_quality_alarm_exception t4
  LEFT JOIN data_quality_exception t5 ON t4.exception_id = t5.id
  GROUP BY t4.alarm_id
) t3 ON t1.id = t3.alarm_id
```

### 2. 数据库索引优化建议

#### 必需索引：
```sql
-- 1. data_quality_alarm表索引
CREATE INDEX idx_alarm_object_type_time ON data_quality_alarm(alarm_object_type, alarm_time DESC);
CREATE INDEX idx_alarm_object_id ON data_quality_alarm(object_id);

-- 2. data_quality_alarm_exception表索引  
CREATE INDEX idx_alarm_exception_alarm_id ON data_quality_alarm_exception(alarm_id);
CREATE INDEX idx_alarm_exception_exception_id ON data_quality_alarm_exception(exception_id);

-- 3. data_quality_exception表索引
CREATE INDEX idx_exception_id ON data_quality_exception(id);

-- 4. bcs_sites表索引
CREATE INDEX idx_sites_id ON bcs_sites(id);
```

#### 复合索引优化：
```sql
-- 覆盖索引，避免回表查询
CREATE INDEX idx_alarm_cover ON data_quality_alarm(alarm_object_type, alarm_time DESC, id, object_id, alarm_info, alarm_state, start_time, end_time, exception_type);

-- 异常关联表复合索引
CREATE INDEX idx_alarm_exception_cover ON data_quality_alarm_exception(alarm_id, exception_id);
```

### 3. 表结构优化建议

#### 冗余字段方案：
```sql
-- 在data_quality_alarm表中增加冗余字段
ALTER TABLE data_quality_alarm ADD COLUMN site_id VARCHAR(50);
ALTER TABLE data_quality_alarm ADD COLUMN exception_count INT DEFAULT 0;
ALTER TABLE data_quality_alarm ADD COLUMN total_duration BIGINT DEFAULT 0;

-- 创建索引
CREATE INDEX idx_alarm_site_id ON data_quality_alarm(site_id);
```

#### 预计算表方案：
```sql
-- 创建预计算统计表
CREATE TABLE data_quality_alarm_stats (
    alarm_id VARCHAR(50) PRIMARY KEY,
    exception_count INT DEFAULT 0,
    total_duration BIGINT DEFAULT 0,
    duration_formatted VARCHAR(20),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_alarm_stats_alarm_id ON data_quality_alarm_stats(alarm_id);
```

### 4. 分区表优化

```sql
-- 按时间分区data_quality_alarm表
CREATE TABLE data_quality_alarm_partitioned (
    -- 原有字段
) PARTITION BY RANGE (alarm_time) (
    PARTITION p202507 VALUES LESS THAN ('2025-08-01'),
    PARTITION p202508 VALUES LESS THAN ('2025-09-01'),
    -- 更多分区...
);
```

### 5. 查询优化技巧

#### 限制返回数据量：
```sql
-- 添加LIMIT，避免全表扫描
SELECT ... FROM ... WHERE ... ORDER BY t1.alarm_time DESC LIMIT 1000;
```

#### 使用EXISTS替代IN：
```sql
-- 优化前
WHERE t2.id in ('id1','id2','id3',...)

-- 优化后  
WHERE EXISTS (
    SELECT 1 FROM (VALUES ('id1'),('id2'),('id3')) AS v(id) WHERE t2.id = v.id
)
```

## 预期性能提升

- **子查询优化**：预计提升60-80%性能
- **索引优化**：预计提升30-50%性能  
- **冗余字段**：预计提升80-90%性能
- **分区表**：预计提升40-60%性能

## 实施建议

1. **立即实施**：代码层面的JOIN优化（已完成）
2. **短期实施**：创建必需索引
3. **中期实施**：表结构优化，添加冗余字段
4. **长期实施**：分区表改造

## 监控建议

```sql
-- 监控慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE query LIKE '%data_quality_alarm%' 
ORDER BY mean_time DESC;

-- 监控索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('data_quality_alarm', 'data_quality_alarm_exception', 'data_quality_exception');
```
